# -*- coding: utf-8 -*-
# Copyright 2025 Imam <PERSON><PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

{
    'name': 'POS Product Modifier',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Enhance POS with customizable product toppings and modifiers for food and beverage businesses',
    'description': """
POS Product Modifier
===================
A comprehensive solution for adding customizable toppings and modifiers to products in Odoo Point of Sale.

Ideal for restaurants, cafes, and food service businesses that need to offer customizable menu items.

Features:
- Define products as toppings with optional quantity limits
- Group toppings for easier management and selection
- Add toppings to products directly from the POS interface
- Automatic topping price calculation in orders
- Intuitive UI for selecting toppings during order entry
- Control whether toppings can be ordered independently
- Reactive state management for smooth user experience

This module seamlessly integrates with the standard Odoo POS workflow while adding powerful
product customization capabilities that modern food service businesses require.
""",
    'author': '<PERSON> <PERSON>',
    'website': '',
    'license': 'AGPL-3',
    'depends': ['point_of_sale', 'product'],
    'data': [
        'security/ir.model.access.csv',
        'views/topping_group_views.xml',
        'views/product_template_views.xml',
        'views/res_config_settings_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'pos_product_modifier/static/src/scss/fields.scss',
            'pos_product_modifier/static/src/scss/form_controller.scss',
        ],
        'point_of_sale._assets_pos': [
            'pos_product_modifier/static/src/**/*',
            ('remove', 'pos_product_modifier/static/src/scss/fields.scss'),
            ('remove', 'pos_product_modifier/static/src/scss/form_controller.scss'),
            ('after', 'point_of_sale/static/src/scss/pos.scss', 'pos_product_modifier/static/src/scss/pos.scss'),
        ],
        'web.qunit_suite_tests': [
            'pos_product_modifier/static/tests/**/*',
        ],
    },
    'demo': [],
    'test': [
        'tests/test_pos_product_modifier.py',
        'tests/test_pos_refund_button.py',
        'tests/test_pos_cash_control_button.py',
        'tests/test_pos_interface_controls.py'
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
