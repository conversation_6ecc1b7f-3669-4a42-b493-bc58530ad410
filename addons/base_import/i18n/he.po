# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# NoaFarkash, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>r, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# ya<PERSON> terner, 2024
# <PERSON> Ketem <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "%s at multiple rows"
msgstr "%s במספר שורות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "%s records successfully imported"
msgstr "%s רשומות יובאו בהצלחה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Additional Fields"
msgstr "שדות נוספים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Advanced"
msgstr "מתקדם"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Allow matching with subfields"
msgstr "לאפשר התאמה עם תתי-שדות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"אירעה בעיה לא ידועה במהלך הייבוא (ייתכן שהחיבור התנתק, חריגה ממגבלת הנתונים "
"או חריגה ממגבלות הזיכרון). נסה שוב אם הבעיה היא זמנית. אם הבעיה עדיין "
"מתרחשת, נסה לפצל את הקובץ במקום לייבא אותו בפעם אחת."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "בסיס"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "ייבוא בסיס"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "מיפוי ייבוא בסיסי"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Batch"
msgstr "אצווה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Batch Import"
msgstr "ייבוא בבת-אחת"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Batch limit"
msgstr "מגבלת אצווה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Cancel"
msgstr "בטל"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr "עמודה %s מכילה ערכים שגויים (ערך: %s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr "עמודה %s מכילה ערכים שגויים. שגיאה בשורה %d: %s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "שם עמודה "

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Comma"
msgstr "פסיק"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Comments"
msgstr "תגובות"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"לא ניתן היה לאחזר את כתובת האתר: %(url)s [%(field_name)s: L%(line_number)d]:"
" %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Create new values"
msgstr "צור ערכים חדשים"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Database ID"
msgstr "מזהה מסד נתונים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Date Format:"
msgstr "פורמט תאריך:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Datetime Format:"
msgstr "פורמט תאריך ושעה:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Decimals Separator:"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Dot"
msgstr "נקודה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Download"
msgstr "הורד"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Encoding:"
msgstr "קידוד:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr "שגיאת ניתוח תאריך [%s:L%d]: %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Error at row %s: \"%s\""
msgstr "שגיאה בשורה %s: \"%s\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %d entries while the first row has %d. You may need to change "
"the separator character."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Estimated time left:"
msgstr "הערכת הזמן שנותר:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Everything seems valid."
msgstr "הכל נראה תקף."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Excel files are recommended as formatting is automatic."
msgstr "מומלץ קבצי אקסל כי הפורמט אוטומטי."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "External ID"
msgstr "מזהה חיצוני"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "שם שדה"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "קובץ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "File Column"
msgstr "עמודת קובץ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "שם קובץ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "סוג קובץ"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "גודל הקובץ חורג מהמקסימום המוגדר (%s בייטים)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr "קובץ כדי לבדוק ו / או לייבא, בינארי גולמי (לא base64)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Finalizing current batch before interrupting..."
msgstr "מסיים את הסבב האחרון לפני הפרעה..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "For CSV files, you may need to select the correct separator."
msgstr "עבור קבצי CSV, ייתכן שתצטרך לבחור במפריד הנכון."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Formatting"
msgstr "פורמט"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"נמצאו נתוני תמונה שאינם תקינים, יש לייבא תמונות ככתובות אתרי אינטרנט או "
"כנתונים מקודדים בבסיס 64."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Go to Import FAQ"
msgstr "לתשובות לשאלות נפוצות על ייבוא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Help"
msgstr "עזרה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr "הנה התחלת הקובץ שלא יכולנו לייבא:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "מזהה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"אם הקובץ מכיל\n"
"                    את שמות העמודות, Odoo יכול לנסות לזהות אוטומטית\n"
"                    את השדה המתאים לעמודה. זה הופך את הייבוא לפשוט\n"
"                    יותר, בייחוד כשלקובץ יש הרבה עמודות."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"אם המודל משתמש בהיסטוריית פעולות פתוחה, מעקב ההיסטוריה יגדיר מנויים וישלח "
"התראות במהלך הייבוא, אך יביא לייבוא ​​איטי יותר."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr "קובץ התמונה גדול מדי, ייבוא תמונות צריך להיות קטן מ-42 מיליון פיקסלים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Import"
msgstr "ייבא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Import FAQ"
msgstr "שאלות נפוצות בייבוא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Import a File"
msgstr "ייבא קובץ"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Import file has no content or is corrupt"
msgstr "אין מידע או שיש תקלה בקובץ המיובא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Import preview failed due to: \""
msgstr "תצוגה מקדימה של הייבוא נכשלה עקב: \" "

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
#, python-format
msgid "Import records"
msgstr "ייבוא רשומות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Imported file"
msgstr "קובץ מיובא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Importing"
msgstr "מייבא"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr "ערך תא לא חוקי בשורה %(row)s, עמודה %(col)s: %(cell_value)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Load File"
msgstr "טען קובץ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Loading file..."
msgstr "טוען קובץ..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
#, python-format
msgid "Loading..."
msgstr "טוען..."

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "מודל"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "Multiple errors occurred"
msgstr "ארעו שגיאות רבות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Need Help?"
msgstr "צריך עזרה?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "No Separator"
msgstr "אין מפריד"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "No matching records found for the following name"
msgstr "לא נמצאו רשומות תואמות לשם הבא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Odoo Field"
msgstr "שדה Odoo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Prevent import"
msgstr "מנע ייבוא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Preview"
msgstr "תצוגה מקדימה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Progress bar"
msgstr "בר התקדמות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Reimport"
msgstr "ייבוא מחדש"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Relation Fields"
msgstr "שדות מקושרים"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "מודל נתונים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Resume"
msgstr "המשך"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Search a field..."
msgstr "חפש שדה..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "See possible values"
msgstr "ראה ערכים אפשריים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Selected Sheet:"
msgstr "גיליון נבחר:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Semicolon"
msgstr "נקודה פסיק"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Separator:"
msgstr "מפריד:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: %s"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: False"
msgstr "הגדר כ: לא נכון"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: True"
msgstr "הגדרה כ: נכון"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set value as empty"
msgstr "קבע ערכים כשדה ריק"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Sheet:"
msgstr "גליון"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Skip record"
msgstr "דלג על הרשומות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Space"
msgstr "רווח"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Standard Fields"
msgstr "שדות סטנדרטים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Start at line"
msgstr "התחל בשורה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Stop Import"
msgstr "הפסקת ייבוא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Suggested Fields"
msgstr "שדות מוצעים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Tab"
msgstr "טאב"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Test"
msgstr "בדוק"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Testing"
msgstr "בדיקה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Text Delimiter:"
msgstr "מפריד טקסט:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "The file contains blocking errors (see below)"
msgstr "הקובץ מכיל שגיאות חסימה (ראה להלן)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "The file will be imported by batches"
msgstr "הקובץ ייובא לפי סבבים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "This column will be concatenated in field"
msgstr "עמודה זו תהיה מחוברת לשדה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Thousands Separator:"
msgstr "מפריד אלפים:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "To import multiple values, separate them by a comma."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "To import, select a field..."
msgstr "לייבוא, בחרו שדה..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Track history during import"
msgstr "עקוב אחר ההיסטוריה במהלך הייבוא"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr "לא ניתן לטעון את הקובץ \"{extension}\": דרוש מודול פייתון \"{modname}\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr "פורמט קובץ לא נתמך \"{}\", ייבוא תומך רק ב- CSV, ODS, XLS ו- XLSX"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Untitled"
msgstr "ללא כותרת"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Upload File"
msgstr "טען קובץ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Upload an Excel or CSV file to import"
msgstr "העלה קובץ אקסל או CSV לייבוא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Use first row as header"
msgstr "שימוש בשורה הראשונה ככותרת"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "משתמש"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr "התראה: יתעלם משורות הכותרת, שורות ריקות ושורות עם תאים ריקים"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
#, python-format
msgid "When a value cannot be matched:"
msgstr "כשלערך אין התאמה"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"אינך יכול לייבא תמונות דרך כתובת אתר האינטרנט, פנה למנהל המערכת שלך או "
"לתמיכה כדי להבין את הסיבה."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "You can test or reload your file before resuming the import."
msgstr "אתה יכול לבדוק או לטעון מחדש את הקובץ שלך לפני שאתה ממשיך לייבא."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "You must configure at least one field to import"
msgstr "עליך להגדיר שדה אחד לפחות לייבוא"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "at multiple rows"
msgstr "במס' שורות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "at row"
msgstr "בשורה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "in field"
msgstr "בשדה"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "minutes"
msgstr "דקות"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "more"
msgstr "עוד"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "out of"
msgstr "מחוץ ל"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "seconds"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "unknown error code %s"
msgstr "קוד שגיאה לא ידוע %s"
