# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2023
# myacc-pro, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"10XXXXXXXXY or 20XXXXXXXXY or 15XXXXXXXXY or 16XXXXXXXXY or 17XXXXXXXXY"
msgstr ""
"10XXXXXXXXY vai 20XXXXXXXXY vai 15XXXXXXXXY vai 16XXXXXXXXY vai 17XXXXXXXXY"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "17291716060 (NIN) or 1729171602 (VKN)"
msgstr "17291716060 (NIN) vai 1729171602 (VKN)"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "1792060346001 or 1792060346"
msgstr "1792060346001 vai 1792060346"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "3101012009"
msgstr "3101012009"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "310175397400003 [Fifteen digits, first and last digits should be \"3\"]"
msgstr ""
"310175397400003 [Piecpadsmit cipari, pirmajam un pēdējam ciparam jābūt "
"\"3\"]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "49-098-576 or 49098576"
msgstr "49-098-576 vai 49098576"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "AR200-5536168-2 or 20055361682"
msgstr "AR200-5536168-2 vai 20055361682"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "CHE-123.456.788 TVA or CHE-123.456.788 MWST or CHE-123.456.788 IVA"
msgstr "CHE-123.456.788 TVA vai CHE-123.456.788 MWST vai CHE-123.456.788 IVA"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "CO213123432-1 or CO213.123.432-1"
msgstr "CO213123432-1 vai CO213.123.432-1"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Uzņēmumi"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"Connection with the VIES server failed. The VAT number %s could not be "
"validated."
msgstr ""
"Savienojums ar VIES serveri neizdevās. PVN numurs %s nevarēja validēt."

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Kontaktpersona"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "DE********8 or 12/345/67890"
msgstr "DE********8 vai 12/345/67890"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "DO1-01-85004-3 or *********"
msgstr "DO1-01-85004-3 vai *********"

#. module: base_vat
#: model:ir.model.fields,help:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,help:base_vat.field_res_users__vies_valid
msgid "European VAT numbers are automatically checked on the VIES database."
msgstr "Eiropas PVN numuri tiek automātiski pārbaudīti VIES datu bāzē."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"Example: '************' (format: 12 digits, all numbers, valid check digit)"
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Nodokļu Profils"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "GB********2 or XI********2"
msgstr "GB********2 vai XI********2"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "HU12345676 or ********-1-11 or **********"
msgstr "HU12345676 vai ********-1-11, vai **********"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, the default fiscal position that applies will "
"depend upon the output of the verification by the European VIES Service."
msgstr ""
"Ja šī izvēles rūtiņa ir atzīmēta, noklusējuma fiskālā pozīcija, kas tiek "
"piemērota, būs atkarīga no Eiropas VIES dienesta veiktās pārbaudes "
"rezultātiem."

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_valid
msgid "Intra-Community Valid"
msgstr "Derīgs starp iekšējiem uzņēmumiem"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "MXGODE561231GR8 or GODE561231GR8"
msgstr "MXGODE561231GR8 vai GODE561231GR8"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__perform_vies_validation
#: model:ir.model.fields,field_description:base_vat.field_res_users__perform_vies_validation
msgid "Perform Vies Validation"
msgstr "Veic VIES validāciju"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_base_vat_form
msgid "Tax ID"
msgstr "PVN Nr."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"%(vat_label)s numurs [%(wrong_vat)s] neizskatās derīgs. \n"
"Piezīme: paredzamais formāts %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"%(vat_label)s numurs [%(wrong_vat)s] priekš %(record_label)s neizskatās derīgs. \n"
"Piezīme: paredzamais formāts %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "The VAT number %s could not be interpreted by the VIES server."
msgstr "VIES serveris nevarēja interpretēt %s PVN numuru."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid ""
"The country detected for this foreign VAT number does not match any of the "
"countries composing the country group set on this fiscal position."
msgstr ""
"Valsts, kas konstatēta šim ārvalstu PVN numuram, neatbilst nevienai no "
"valstīm, kas veido šai fiskālajai pozīcijai iestatīto valstu grupu."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid ""
"The country of the foreign VAT number could not be detected. Please assign a"
" country to the fiscal position or set a country group"
msgstr ""
"Valsts ar ārzemju PVN numuru netika atrasta. Lūdzu, piešķiriet valsti "
"fiskālajai pozīcijai vai iestatiet valstu grupu"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The request for VAT validation was not processed. VIES service has responded"
" with the following error: %s"
msgstr ""
"Pieprasījums PVN validācijai nav apstrādāts. VIES pakalpojums atbildēja ar "
"sekojošu kļūdu: %s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "VAT"
msgstr "PVN"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Verificēt PVN numurus"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Verificēt PVN numurus izmantojot Eiropas VIES pakalpojumu"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_vat_to_check
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_vat_to_check
msgid "Vies Vat To Check"
msgstr "VIES PVN pārbaudei"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "XXXXXXXXX [9 digits] and it should respect the Luhn algorithm checksum"
msgstr "XXXXXXXXX [9 skaitļi] un tam ir jāievēro Luhn algoritma kontrolsumma"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "either 11 digits for CPF or 14 digits for CNPJ"
msgstr "vai 11 skaitļi priekš CPF vai 14 skaitļi priekš CNPJ"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid "fiscal position [%s]"
msgstr "fiskālā pozīcija [%s]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "partner [%s]"
msgstr "partneris [%s]"
