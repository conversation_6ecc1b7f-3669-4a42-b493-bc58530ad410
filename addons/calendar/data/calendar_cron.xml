<?xml version="1.0"?>
<odoo>
    <data noupdate="1">
        <!-- Scheduler for Event Alarm-->
        <record forcecreate="True" id="ir_cron_scheduler_alarm" model="ir.cron">
            <field name="name">Calendar: Event Reminder</field>
            <field name="model_id" ref="model_calendar_alarm_manager"/>
            <field name="state">code</field>
            <field name="code">model._send_reminder()</field>
            <field eval="True" name="active" />
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field eval="False" name="doall" />
        </record>
    </data>
</odoo>
