# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_calendar
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2017-10-10 11:35+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"Language: sq\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "%(id)s and %(length)s following"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Days"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "(No title)"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "An administrator needs to configure Google Synchronization before you can use it!"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid "An error occurred while generating the token. Your authorization code may be invalid or has already expired [%s]. You should check your Client ID and secret on the Google APIs plateform or try to stop and restart your calendar synchronisation."
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
msgid "Calendar ID"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Configuration"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_uid
msgid "Created by"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_date
msgid "Created on"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__display_name
msgid "Display Name"
msgstr "Emri i paraqitur"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Email"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
#, python-format
msgid "Google"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_account_id
msgid "Google Calendar Account"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_credentials
msgid "Google Calendar Account Data"
msgstr ""

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr ""

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Google Calendar: synchronization"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "Google gave the following explanation: %s"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid "If the active field is set to false, it will allow you to hide the event alarm information without removing it."
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "It will not be synced as long at it is not updated."
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
msgid "Last Calendar ID who has been synchronized. If it is changed, we remove all links between GoogleID and Odoo Google Internal ID"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_uid
msgid "Last Updated by"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_date
msgid "Last Updated on"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
msgid "Next Sync Token"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Notification"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
msgid "Refresh Token"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Success"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "The Google Synchronization needs to be configured before you can use it, do you want to do it now?"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid "The account for the Google Calendar service is not configured."
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "The following event could not be synced with Google Calendar."
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "The synchronization with Google calendar was successfully stopped."
msgstr ""

#. module: google_calendar
#: model:ir.model.constraint,message:google_calendar.constraint_res_users_google_token_uniq
msgid "The user has already a google account"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
msgid "Token Validity"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__user_ids
msgid "User"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
msgid "User token"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "You are about to stop the synchronization of your calendar with Google. Are you sure you want to continue?"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "undefined time"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "you don't seem to have permission to modify this event on Google Calendar"
msgstr ""
