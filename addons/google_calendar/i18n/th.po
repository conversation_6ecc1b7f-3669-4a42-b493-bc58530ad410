# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON>il <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>iam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "%(id)s and %(length)s following"
msgstr "%(id)s และ %(length)s ติดตาม"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s วัน"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s ชั่วโมง"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s นาที"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "(No title)"
msgstr "(ไม่มีชื่อเรื่อง)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"ผู้ดูแลระบบต้องกำหนดค่า Google Synchronization ก่อน คุณจึงจะสามารถใช้งานได้!"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronisation."
msgstr ""
"เกิดข้อผิดพลาดขณะสร้างโทเค็น "
"โค้ดการให้สิทธิ์ของคุณอาจไม่ถูกต้องหรือหมดอายุแล้ว [%s] "
"คุณควรตรวจสอบไอดีลูกค้าและรหัสบนแพลตฟอร์ม Google APIs "
"หรือพยายามหยุดและเริ่มต้นการซิงโครไนซ์ปฏิทินของคุณใหม่"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "ปฏิทินข้อมูลผู้เข้าร่วม"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "ปฎิทินอีเวนต์"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
msgid "Calendar ID"
msgstr "ปฏิทินไอดี"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "ไอดีลูกค้า"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "ความลับลูกค้า"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Configure"
msgstr "กำหนดค่า"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "ลบจาก Odoo "

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "ลบจากทั้งสอง"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr "ลบออกจากบัญชีปฏิทิน Google ปัจจุบัน"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Discard"
msgstr "ละทิ้ง"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Email"
msgstr "อีเมล"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "กฎการเกิดซ้ำของอีเวนต์"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
#, python-format
msgid "Google"
msgstr "Google"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "ปฏิทิน Google "

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_account_id
msgid "Google Calendar Account"
msgstr "บัญชี Google Calendar"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_credentials
msgid "Google Calendar Account Data"
msgstr "ข้อมูลบัญชีปฏิทิน Google"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr "ตั้งค่าบัญชีปฏิทิน Google ใหม่"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "ไอดีปฏิทินอีเวนต์ Google"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr "ไอดีปฏิทิน Google "

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Google Calendar: synchronization"
msgstr "ปฏิทิน Google : การซิงโครไนซ์"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__calendar_event__videocall_source__google_meet
msgid "Google Meet"
msgstr "Google Meet"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "การซิงโครไนซ์ของ Google หยุดชั่วคราว"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr "การซิงโครไนซ์ Google หยุด"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "Google gave the following explanation: %s"
msgstr "Google ได้ให้คำอธิบายดังต่อไปนี้: %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__guests_readonly
msgid "Guests Event Modification Permission"
msgstr "สิทธิ์ในการปรับเปลี่ยนกิจกรรมของผู้เข้าร่วม"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__id
msgid "ID"
msgstr "ไอดี"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"หากฟิลด์ที่ใช้งานอยู่ถูกตั้งค่าเป็น \"เท็จ\" "
"จะช่วยให้คุณสามารถซ่อนข้อมูลการเตือนเหตุการณ์โดยไม่ต้องลบออก"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Indicates if synchronization with Google Calendar is paused or not."
msgstr "ระบุว่าการซิงโครไนซ์กับ Google Calendar ถูกหยุดชั่วคราวหรือไม่"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "It will not be synced as long at it is not updated."
msgstr "มันจะไม่ถูกซิงค์ตราบใดที่ไม่ได้อัปเดต"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"ไอดีปฏิทินล่าสุดที่ได้รับการซิงโครไนซ์ หากมีการเปลี่ยนแปลง "
"เราจะลบลิงก์ทั้งหมดระหว่าง GoogleID และ Odoo Google Internal ID"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "ปล่อยไว้ไม่แตะต้อง"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr "ต้องการการซิงค์"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
msgid "Next Sync Token"
msgstr "การซิงค์โทเค็นถัดไป"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "การซิงโครไนซ์ถัดไป"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Notification"
msgstr "การแจ้งเตือน"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr "หยุดการซิงโครไนซ์ชั่วคราว"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
msgid "Refresh Token"
msgstr "รีเฟรชโทเค็น"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr "ตั้งค่าบัญชีใหม่"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr "ตั้งค่าบัญชีปฏิทิน Google ใหม่"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Stop Synchronization"
msgstr "หยุดการซิงโครไนซ์"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Success"
msgstr "สำเร็จ"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr "ซิงโครไนซ์บันทึกกับปฏิทิน Google "

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "ซิงโครไนซ์อีเวนต์ที่มีอยู่ทั้งหมด"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "ซิงโครไนซ์อีเวนต์ใหม่ทั้งหมด"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"จำเป็นต้องกำหนดค่า Google Synchronization ก่อน คุณจึงจะสามารถใช้งานได้ "
"คุณต้องการทำตอนนี้หรือไม่?"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid "The account for the Google Calendar service is not configured."
msgstr "ไม่ได้กำหนดค่าบัญชีสำหรับบริการปฏิทิน Google "

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid ""
"The following event can only be updated by the organizer according to the "
"event permissions set on Google Calendar."
msgstr ""
"กิจกรรมต่อไปนี้สามารถอัปเดตได้โดยผู้จัดงานเท่านั้นตามสิทธิ์ของกิจกรรมที่ตั้งไว้ใน"
" Google Calendar"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "The following event could not be synced with Google Calendar."
msgstr "กิจกรรมต่อไปนี้ไม่สามารถซิงค์กับ Google Calendar ได้"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "The synchronization with Google calendar was successfully stopped."
msgstr "หยุดการซิงโครไนซ์กับปฏิทิน Google สำเร็จแล้ว"

#. module: google_calendar
#: model:ir.model.constraint,message:google_calendar.constraint_res_users_google_token_uniq
msgid "The user has already a google account"
msgstr "ผู้ใช้มีบัญชี Google แล้ว"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "ซึ่งจะมีผลกับอีเวนต์ที่ผู้ใช้เป็นเจ้าของเท่านั้น"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
msgid "Token Validity"
msgstr "ความถูกต้องของโทเค็น"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__user_ids
msgid "User"
msgstr "ผู้ใช้"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
msgid "User token"
msgstr "โทเค็นผู้ใช้"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "อีเวนต์ที่มีอยู่ของผู้ใช้"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "แหล่งที่มาของวิดีโอคอล"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid ""
"You are about to stop the synchronization of your calendar with Google. Are "
"you sure you want to continue?"
msgstr ""
"คุณกำลังจะหยุดการซิงโครไนซ์ปฏิทินของคุณกับ Google "
"คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Your administrator paused the synchronization with Google Calendar."
msgstr "ผู้ดูแลระบบของคุณหยุดการซิงโครไนซ์กับ Google Calendar ชั่วคราว"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "undefined time"
msgstr "ไม่ได้กำหนดเวลา"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr "ดูเหมือนคุณจะไม่ได้รับอนุญาตให้แก้ไขอีเวนต์นี้ในปฏิทิน Google "
