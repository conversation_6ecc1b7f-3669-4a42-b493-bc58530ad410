<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fetchmail_server_view_form" model="ir.ui.view">
        <field name="name">fetchmail.server.view.form.inherit.gmail</field>
        <field name="model">fetchmail.server</field>
        <field name="priority">100</field>
        <field name="inherit_id" ref="mail.view_email_server_form"/>
        <field name="arch" type="xml">
            <field name="user" position="after">
                <field name="google_gmail_uri" invisible="1"/>
                <field name="google_gmail_refresh_token" invisible="1"/>
                <div invisible="server_type != 'gmail'"
                    class="d-flex flex-row align-items-center" colspan="2">
                    <span invisible="server_type != 'gmail' or not google_gmail_refresh_token"
                        class="badge text-bg-success">
                        Gmail Token Valid
                    </span>
                    <button type="object"
                        name="open_google_gmail_uri" class="btn-link px-0"
                        invisible="not google_gmail_uri or server_type != 'gmail' or google_gmail_refresh_token">
                        <i class="oi oi-arrow-right"/>
                        Connect your Gmail account
                    </button>
                    <button type="object"
                        name="open_google_gmail_uri" class="btn-link px-0 ms-2"
                        invisible="not google_gmail_uri or server_type != 'gmail' or not google_gmail_refresh_token">
                        <i class="fa fa-cog" title="Edit Settings"/>
                    </button>
                    <button class="alert alert-warning d-block mt-2 text-start"
                        icon="oi-arrow-right" type="action" role="alert"
                        name="%(base.res_config_setting_act_window)d"
                        invisible="server_type != 'gmail' or google_gmail_uri">
                        Setup your Gmail API credentials in the general settings to link a Gmail account.
                    </button>
                </div>
            </field>
        </field>
    </record>
</odoo>
