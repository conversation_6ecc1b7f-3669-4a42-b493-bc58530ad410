# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:31+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "Ora de ieșire nu poate fi mai mică decât ora de intrare."

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%s : (%s-%s)"
msgstr "%s : (%s-%s)"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<br/>\n"
"                            <span>\n"
"                                If your address is compromised, you can refresh it to generate a new one.\n"
"                            </span>\n"
"                            <br/>"
msgstr ""
"<br/>\n"
"                            <span>\n"
"                                Dacă adresa dvs. este compromisă, o puteți actualiza pentru a genera una nouă.\n"
"                            </span>\n"
"                            <br/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            This Month\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Luna aceasta\n"
"                        </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr "<span class=\"o_stat_text\">Ore suplimentare</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"Available\" title=\"Available\"/>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"Disponibil\" title=\"Disponibil\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Indisponibil\" title=\"Indisponibil\">\n"
"                                    </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr "<span> Minute</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span> Minutes</span>\n"
"                                <br/>\n"
"                                <br/>"
msgstr ""
"<span> Minute</span>\n"
"                                <br/>\n"
"                                <br/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> seconds</span>"
msgstr "<span> secunde</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span>Time Period </span>"
msgstr "<span>Perioada de timp </span>"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction
msgid "Action Needed"
msgstr "Intervenție necesară"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Activate the count of employees' extra hours."
msgstr "Activați numărarea orelor suplimentare ale angajaților."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"Adăugați câțiva angajați pentru a putea selecta un angajat aici și efectua check-in / check-out.\n"
"                Pentru a crea angajați, accesați meniul Angajați."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "Ajustare"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#, python-format
msgid "All"
msgstr "Tot"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Allow Users to Check in/out from Odoo."
msgstr "Permiteți utilizatorilor să efectueze check-in/out din Odoo."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr ""
"Permite o perioadă de timp (în jurul orelor de lucru) în care timpul "
"suplimentar nu va fi numărat, în beneficiul companiei"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr ""
"Permite o perioadă de timp (în jurul orelor de lucru) în care timpul "
"suplimentar nu va fi dedus, în beneficiul angajatului"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr "Cantitatea de ore suplimentare"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "At Work"
msgstr "La muncă"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_tree_inherit_leave
#, python-format
msgid "Attendance"
msgstr "Prezență"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_from_systray
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_from_systray
msgid "Attendance From Systray"
msgstr "Participare din Systray"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_delay
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_delay
msgid "Attendance Kiosk Delay"
msgstr "Întârziere la punctul de prezență"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_key
msgid "Attendance Kiosk Key"
msgstr "Prezență Kiosk Key"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_url
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_url
msgid "Attendance Kiosk Url"
msgstr "Prezență Kiosk Url"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_manager_id
msgid "Attendance Manager"
msgstr "Manager de prezență"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_mode
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_mode
msgid "Attendance Mode"
msgstr "Modul de prezență"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr "Prezență în timpul programului"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "Stare Prezență"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_reporting
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "Prezențe"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#: code:addons/hr_attendance/models/res_users.py:0
#, python-format
msgid "Attendances This Month"
msgstr "Prezențe în această lună"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances from Backend"
msgstr "Prezențe din Backend"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__back
msgid "Back Camera"
msgstr "Camera spate"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode
msgid "Barcode / RFID"
msgstr "Cod de bare / RFID"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode_manual
msgid "Barcode / RFID and Manual Selection"
msgstr "Cod de bare / RFID și selecție manuală"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_barcode_source
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_barcode_source
msgid "Barcode Source"
msgstr "Sursă cod de bare"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__in_country_name
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__out_country_name
msgid "Based on IP Address"
msgstr "Bazat pe adresă IP"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_base
msgid "Basic Employee"
msgstr "BAngajat de bază"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Before"
msgstr "Înainte"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_browser
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Browser"
msgstr "Browser"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"Nu se poate crea o nouă prezență pentru %(empl_name)s, angajatul nu a ieșit "
"după %(datetime)s"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"Nu se poate crea o nouă prezență pentru %(empl_name)s , angajatul este deja "
"intrat de la %(datetime)s"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"Nu se poate efectua check-out-ul lui%(empl_name)s, Nu s-a putut găsi "
"înregistrarea corespunzătoare. Participanții dvs. au fost probabil "
"modificați manual de resurse umane."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
#, python-format
msgid "Check IN"
msgstr "Check IN"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check In"
msgstr "Intrare"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
#, python-format
msgid "Check OUT"
msgstr "Check OUT"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check Out"
msgstr "Ieșire"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Check in"
msgstr "Check in"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Check out"
msgstr "Check out"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "Intrat"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Checked in at"
msgstr "Intrat la"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "Ieșit"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Checked out at"
msgstr "Ieșire la"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Choose how long the greeting message will be displayed."
msgstr "Alegeți cât timp va fi afișat mesajul de salut."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_city
msgid "City"
msgstr "Localitate"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__color
msgid "Color"
msgstr "Culoare"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
msgid "Company"
msgstr "Companie"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Company Logo"
msgstr "Siglă comanie"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Compare attendance with working hours set on employee."
msgstr "Comparați prezența cu orele de lucru stabilite pentru angajat."

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "Configurare"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr "Numără orele suplimentare"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Count of Extra Hours"
msgstr "Numărul de ore suplimentare"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Count of extra hours is considered from this date. Potential extra hours "
"prior to this date are not considered."
msgstr ""
"Numărul de ore suplimentare este considerat de la această dată. Orele "
"suplimentare potențiale înainte de această dată nu sunt luate în "
"considerare."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_country_name
msgid "Country"
msgstr "Țară"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "Creați un nou angajat"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "Zi"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the camera used for the barcode scan."
msgstr "Definiți camera folosită pentru scanarea codului de bare."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the way the user will be identified by the application."
msgstr "Definiți modul în care utilizatorul va fi identificat de aplicație."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
#, python-format
msgid "Department"
msgstr "Departament"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__display_extra_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours"
msgstr "Afișați orele suplimentare"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours in Kiosk mode and on User profile."
msgstr ""
"Afișați ore suplimentare în modul Kiosk și în profilul utilizatorului."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours."
msgstr "Afișați orele suplimentare."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Time"
msgstr "Afișează timpul"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Do not have access, user cannot edit the attendances that are not his own."
msgstr ""
"Nu are acces, utilizatorul nu poate edita prezențele care nu sunt ale sale."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
#, python-format
msgid "Employee"
msgstr "Angajat"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_use_pin
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_use_pin
msgid "Employee PIN Identification"
msgstr "Codul PIN de identificare a angajatului"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr "Prezența salariaților"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "Angajați"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Error: could not find corresponding employee."
msgstr "Eroare: nu am putut găsi angajatul corespunzător."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Errors"
msgstr "Erori"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours"
msgstr "Ore suplimentare"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr "Ore suplimentare (reale)"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_start_date
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_start_date
msgid "Extra Hours Starting Date"
msgstr "Data de începere a orelelor suplimentare"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Extra hours today:"
msgstr "Ore suplimentare astăzi:"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr "Ore suplimentare incluzând durata pragului"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_follower_ids
msgid "Followers"
msgstr "Persoane interesate"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "From %s"
msgstr "De la %s"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__front
msgid "Front Camera"
msgstr "Camera frontală"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "GPS Coordinates"
msgstr "Coordonate GPS"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Generate a new Kiosk Mode URL"
msgstr "Generați o nouă adresă URL pentru modul Kiosk"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Go back"
msgstr "Înapoi"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Goodbye"
msgstr "La revedere"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "Grupează după"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "Ore"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr "Ore Luna Trecută"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr "Afișare Ore Luna Trecută"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_previously_today
msgid "Hours Previously Today"
msgstr "Ore anterioare astăzi"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Hours Previously Today:"
msgstr "Ore anterioare astăzi:"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr "Ore Astăzi"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Hours Today :"
msgstr "Ore astăzi :"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "Căutare prezență angajați"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_ip_address
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "IP Address"
msgstr "Adresă IP"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Identify Manually"
msgstr "Identificat Manual"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Dacă este selectat, mesajele noi necesită atenția dumneavoastră."

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, unele mesaje au o eroare de livrare."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Invalid request"
msgstr "Cerere invalidă"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__kiosk
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__kiosk
msgid "Kiosk"
msgstr "Chioșc"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode"
msgstr "Mod chioșc"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode Adress"
msgstr "Adresa mod Kiosk"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Settings"
msgstr "Setări Kiosk"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Last 3 Months"
msgstr "Ultimele 3 luni"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Last 7 days"
msgstr "Ultimele 7 zile"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "Ultima pezență"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_worked_hours
msgid "Last Attendance Worked Hours"
msgstr "Ultima prezență a orelor de lucru"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_latitude
msgid "Latitude"
msgstr "Latitudine"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Localisation"
msgstr "Localizare"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_longitude
msgid "Longitude"
msgstr "Longitudine"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__manual
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__manual
msgid "Manual"
msgstr "Manual"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__manual
msgid "Manual Selection"
msgstr "Selectare Manuală"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "Mesaj"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error
msgid "Message Delivery error"
msgstr "Eroare livrare mesaj"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_mode
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Mode"
msgstr "Mod"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Modes"
msgstr "Moduri"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "Prezența mea"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Team"
msgstr "Echipa mea"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "No attendance records found"
msgstr "Nu există înregistrări despre prezență"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
#, python-format
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr "Niciun angajat care să corespundă acestui Badge ID '%(barcode)s.'"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of errors"
msgstr "Număr de erori"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numărul de mesaje care necesită acțiune"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_officer
msgid "Officer: Manage attendances"
msgstr "Ofițer: Gestionarea prezențelor"

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.open_kiosk_url
msgid "Open Kiosk Url"
msgstr "Deschide Kiosk Url"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_browser
msgid "Out Browser"
msgstr "Browser-ul de Ieșire"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_city
msgid "Out City"
msgstr "Orașul de Ieșire"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_country_name
msgid "Out Country Name"
msgstr "Țara de Ieșire"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_ip_address
msgid "Out Ip Address"
msgstr "Adresă IP de Ieșire"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_latitude
msgid "Out Latitude"
msgstr "Latitudine de Ieșire"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_longitude
msgid "Out Longitude"
msgstr "Longitudine de Ieșire"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_mode
msgid "Out Mode"
msgstr "Mod Ieșire"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__overtime_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Over Time"
msgstr "Timp peste program"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#: code:addons/hr_attendance/models/res_users.py:0
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
#, python-format
msgid "Overtime"
msgstr "Suplimentare"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
msgid "Overview"
msgstr "Prezentare generală"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Please enter your PIN to"
msgstr "Vă rugăm să introduceți codul PIN la"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Please return to the main menu."
msgstr "Vă rugăm să reveniți la meniul principal."

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "Angajați Publici "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__rating_ids
msgid "Ratings"
msgstr "Evaluări"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_reporting
msgid "Reporting"
msgstr "Raportare"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Scanați ecusonul dvs."

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__scanner
msgid "Scanner"
msgstr "Scaner"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#, python-format
msgid "Search..."
msgstr "Caută..."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr ""
"Setați codurile PIN în formularul cu detalii despre angajați (în fila Setări"
" HR)."

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "Setări"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Sign out"
msgstr "Iesire"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Since"
msgstr "De când"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Start from"
msgstr "Start de la"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__systray
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__systray
msgid "Systray"
msgstr "Systray"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "The attendance records of your employees will be displayed here."
msgstr "Prezențele angajaților dvs. vor fi afișate aici."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "The attendance reporting of your employees will be displayed here."
msgstr "Raportul de prezență al angajaților dvs. va fi afișat aici."

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_res_users__attendance_manager_id
msgid ""
"The user set in Attendance will access the attendance of the employee "
"through the dedicated app and will be able to edit them."
msgstr ""
"Utilizatorul setat în Attendance va avea acces la prezențele angajatului "
"prin intermediul aplicației dedicate și va putea să le editeze."

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_own_reader
msgid ""
"The user will have access to his own attendances on his user / employee "
"profile"
msgstr ""
"Utilizatorul va avea acces la propriile prezențe pe profilul său de "
"utilizator/angajat"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_officer
msgid ""
"The user will have access to the attendance records and reporting of "
"employees where he's set as an attendance manager"
msgstr ""
"Utilizatorul va avea acces la înregistrările și rapoartele de prezență ale "
"angajaților pentru care este setat ca manager de prezență"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr "Timpul de toleranță în favoarea companiei"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr "Timpul de toleranță în favoarea angajatului"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr "Timp suplimentar total"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Total extra hours:"
msgstr "Ore suplimentare totale:"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Total today"
msgstr "Total astăzi"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/controllers/main.py:0
#: code:addons/hr_attendance/controllers/main.py:0
#, python-format
msgid "Unknown"
msgstr "Necunoscut"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes (defined on the Employee's profile) to check-in."
msgstr ""
"Utilizați coduri PIN (definite în profilul angajatului) pentru a intra."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Use this url to access your kiosk mode from any device. Warning, anybody "
"with the link can access your kiosk."
msgstr ""
"Utilizați această adresă URL pentru a-ți accesa modul tau kiosk de pe orice "
"dispozitiv. Atenție, oricine are acest link vă poate accesa kiosk-ul."

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
msgid "User"
msgstr "Utilizator"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_own_reader
msgid "User: Read his own attendances"
msgstr "Utilizator: Își citește propriile prezențe"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "View on Maps"
msgstr "Vezi pe hartă"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Want to check out?"
msgstr "Doriți să ieșiți?"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website Messages"
msgstr "Mesaje Website"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website communication history"
msgstr "Istoric comunicare website"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Welcome"
msgstr "Bine ați venit"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Welcome to"
msgstr "Bun venit la"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Welcome!"
msgstr "Bine ați venit!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_employee_simple_tree_view
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Work Hours"
msgstr "Ore de Lucru"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
msgid "Worked Hours"
msgstr "Ore lucrare"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours this month"
msgstr "Ore lucrate luna aceasta"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
#, python-format
msgid "Wrong Pin"
msgstr "Pin greșit"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "You cannot duplicate an attendance."
msgstr "Nu puteți duplica o prezență"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "check in"
msgstr "check in"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "check out"
msgstr "check out"
