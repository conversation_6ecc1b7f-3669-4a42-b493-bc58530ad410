# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2023
# R<PERSON> Consulting <<EMAIL>>, 2023
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# ma<PERSON><PERSON>, 2023
# Noemi <PERSON>la, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:31+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Noemi <PERSON>, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
msgid "<span class=\"o_stat_text\">Org Chart</span>"
msgstr ""

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid "Add a new employee"
msgstr "Afegiu un nou empleat"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleat bàsic"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__department_color
msgid "Department Color"
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_count
msgid "Direct Subordinates Count"
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr "Subordinació directa i indirecta"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Direct subordinates"
msgstr "Subordinats directes"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "Empleat"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_department_hierarchy_view
msgid "Employees"
msgstr "Empleats"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "In order to get an organigram, set a manager and save the record."
msgstr ""
"Per tal d'obtenir un organigrama, establir un gestor i salvar el registre."

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr "Comptador de subordenades indirectes"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Indirect subordinates"
msgstr "Subordinacions indirectes"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__is_subordinate
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__is_subordinate
msgid "Is Subordinate"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "More managers"
msgstr "Més gestors"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "No hierarchy position."
msgstr "Cap posició de jerarquia."

#. module: hr_org_chart
#. odoo-python
#: code:addons/hr_org_chart/models/hr_org_chart_mixin.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operació no implementada"

#. module: hr_org_chart
#: model:ir.actions.act_window,name:hr_org_chart.action_hr_employee_org_chart
#: model:ir.ui.menu,name:hr_org_chart.menu_hr_employee_org_chart
msgid "Org Chart"
msgstr ""

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr "Diagrama d'organitzacions"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleat públic"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Redirect"
msgstr "Redirigir"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "See All"
msgstr "Veure Tot"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr "Subordinats"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hooks.js:0
#, python-format
msgid "Team"
msgstr "Equip"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "This employee has no manager or subordinate."
msgstr "Aquest empleat no té administrador ni subordinat."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""
"Amb una vista rapida a la pantalla d'empleats d'Odoo podrà trobar fàcilment "
"tota la informació que necessiti sobre cada persona: informació de contacte,"
" lloc de treball, disponibilitat, etc..."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_card.xml:0
#, python-format
msgid "people"
msgstr "persones"
