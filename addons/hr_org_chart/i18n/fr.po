# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:31+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
msgid "<span class=\"o_stat_text\">Org Chart</span>"
msgstr "<span class=\"o_stat_text\">Organigramme</span>"

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid "Add a new employee"
msgstr "Ajouter un nouvel employé"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr "Employé de base"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__department_color
msgid "Department Color"
msgstr "Couleur du département"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_count
msgid "Direct Subordinates Count"
msgstr "Nombre de subordonnés directs"

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr "Subordonnés directs et indirects"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Direct subordinates"
msgstr "Subordonnés directs"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "Employé"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_department_hierarchy_view
msgid "Employees"
msgstr "Employés"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "In order to get an organigram, set a manager and save the record."
msgstr ""
"Afin d'obtenir un organigramme, ajoutez un responsable et enregistrez."

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr "Nombre de subordonnés indirects"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Indirect subordinates"
msgstr "Subordonnés indirects"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__is_subordinate
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__is_subordinate
msgid "Is Subordinate"
msgstr "Est subordonné"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "More managers"
msgstr "Plus de managers"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "No hierarchy position."
msgstr "Aucune position hiérarchique."

#. module: hr_org_chart
#. odoo-python
#: code:addons/hr_org_chart/models/hr_org_chart_mixin.py:0
#, python-format
msgid "Operation not supported"
msgstr "Opération non prise en charge"

#. module: hr_org_chart
#: model:ir.actions.act_window,name:hr_org_chart.action_hr_employee_org_chart
#: model:ir.ui.menu,name:hr_org_chart.menu_hr_employee_org_chart
msgid "Org Chart"
msgstr "Organigramme"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr "Organigramme"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr "Fonctionnaire"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Redirect"
msgstr "Redirection"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "See All"
msgstr "Voir tout"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr "Subordonnés"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hooks.js:0
#, python-format
msgid "Team"
msgstr "Équipe"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "This employee has no manager or subordinate."
msgstr "Cet employé n'a pas de responsable ni de subordonné."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""
"D'un coup d’œil rapide à l'écran d'employé d'Odoo, vous\n"
"pouvez aisément trouver toutes les informations dont vous avez besoin pour chaque personne ; \n"
"coordonnées, poste, disponibilité, etc."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_card.xml:0
#, python-format
msgid "people"
msgstr "personnes"
