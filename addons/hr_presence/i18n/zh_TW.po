# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_presence
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_presence
#: model:mail.template,body_html:hr_presence.mail_template_presence
msgid ""
"<div>\n"
"                    Dear <t t-out=\"object.name or ''\"><PERSON></t>,<br><br>\n"
"                    Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.<br>\n"
"                    Please, take appropriate measures in order to carry out this work absence.<br>\n"
"                    Do not hesitate to contact your manager or the human resource department.\n"
"                    <br>Best Regards,<br><br>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    親愛的 <t t-out=\"object.name or ''\">Abigail Peterson</t>：<br><br>\n"
"                    若非我們出錯，似乎你不在辦公室，系統亦沒有你的請假記錄。<br>\n"
"                    請採取適當措施，補回請假或解決這次缺勤。<br>\n"
"                    歡迎隨時聯絡你的主管或人力資源部門。\n"
"                    <br>順祝  安康<br><br>\n"
"                </div>\n"
"            "

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Absence/Presence"
msgstr "缺席/出勤"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__absent
msgid "Absent"
msgstr "病假"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_hr_employee_base
msgid "Basic Employee"
msgstr "一般員工"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_company
msgid "Companies"
msgstr "公司"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Compose Email"
msgstr "撰寫電郵"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_actions_server_action_open_presence_view
msgid "Compute presence and open presence view"
msgstr "計算出勤和打開出勤介面。"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__create_uid
msgid "Create Uid"
msgstr "建立 Uid"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__email_sent
msgid "Email Sent"
msgstr "電郵已發送"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Employee's Presence to Define"
msgstr "要定義的員工出勤"

#. module: hr_presence
#: model:sms.template,name:hr_presence.sms_template_data_hr_presence
msgid "Employee: Presence Reminder"
msgstr "員工：出勤狀況提醒"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Employees"
msgstr "員工"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#: model:sms.template,body:hr_presence.sms_template_data_hr_presence
#, python-format
msgid ""
"Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.\n"
"Please, take appropriate measures in order to carry out this work absence.\n"
"Do not hesitate to contact your manager or the human resource department."
msgstr ""
"您似乎不在辦公室，也沒有要求請假。\n"
"請採取適當的措施來進行這項工作缺席。\n"
"若有任何問題，請盡速聯繫您的經理或人力資源部門。"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_cron_presence_control_ir_actions_server
msgid "HR Presence: cron"
msgstr "人力資源出勤：cron"

#. module: hr_presence
#: model:mail.template,name:hr_presence.mail_template_presence
msgid "HR: Employee Absence email"
msgstr "人力資源：員工缺勤電郵"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_company__hr_presence_last_compute_date
msgid "Hr Presence Last Compute Date"
msgstr "人員出勤狀態前次更新時間"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__hr_presence_state_display
msgid "Hr Presence State Display"
msgstr "人員出勤狀態顯示"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__ip
msgid "IP Address"
msgstr "IP 位址"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__ip_connected
msgid "Ip Connected"
msgstr "Ip 已連接"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Log"
msgstr "日誌"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__manually_set_present
msgid "Manually Set Present"
msgstr "手動設置為出勤"

#. module: hr_presence
#: model:ir.ui.menu,name:hr_presence.menu_hr_presence_view
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Presence"
msgstr "出勤"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__present
msgid "Present"
msgstr "現在"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "SMS"
msgstr "電話短訊（SMS）"

#. module: hr_presence
#: model:mail.template,description:hr_presence.mail_template_presence
msgid ""
"Sent manually in presence module when an employee wasn't working despite not"
" being off"
msgstr "當員工不工作時，儘管沒有休假，在出勤模組中手動發送"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as absent"
msgstr "設為缺席"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as present"
msgstr "設置為出勤"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional email address for this employee."
msgstr "此員工未設定電子郵件地址。"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional mobile for this employee."
msgstr "此員工沒有設置手機號碼。"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Time Off"
msgstr "休假"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__to_define
msgid "To Define"
msgstr "定義"

#. module: hr_presence
#: model:mail.template,subject:hr_presence.mail_template_presence
msgid "Unexpected Absence"
msgstr "未預期的缺勤"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_users_log
msgid "Users Log"
msgstr "使用者紀錄"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "You don't have the right to do this. Please contact an Administrator."
msgstr "你沒有權限進行此作業。請與人資管理員聯繫。"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_ir_websocket
msgid "websocket message handling"
msgstr "WebSocket 訊息處理"
