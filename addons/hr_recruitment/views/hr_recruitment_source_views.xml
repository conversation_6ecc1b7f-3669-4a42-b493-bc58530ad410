<?xml version="1.0"?>
<odoo>
    <data>
        <record id="hr_recruitment_source_kanban" model="ir.ui.view">
            <field name="name">hr.recruitment.source.kanban</field>
            <field name="model">hr.recruitment.source</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" create="0" sample="1">
                    <field name="job_id"/>
                    <field name="email"/>
                    <field name="has_domain"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <div class="o_kanban_record_headings row">
                                            <div class="col-4">
                                                <h3 class="o_kanban_record_title"><field name="source_id"/></h3>
                                            </div>
                                            <div class="col-8 text-end">
                                                <div><field name="job_id"/></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body mt-3">
                                        <div class="text-end">
                                            <field name="email" invisible="not has_domain" widget="email"/>
                                            <button name="create_alias" class="btn btn-primary mb-2" type="object" invisible="not has_domain or email">Generate Email</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.ui.view" id="hr_recruitment_source_tree">
            <field name="name">hr.recruitment.source.tree</field>
            <field name="model">hr.recruitment.source</field>
            <field name="arch" type="xml">
                <tree string="Sources of Applicants" editable="top" sample="1">
                    <field name="has_domain" column_invisible="True"/>
                    <field name="source_id" placeholder="e.g. LinkedIn" decoration-bf="1" readonly="id"/>
                    <field name="medium_id" optional="hidden"/>
                    <field name="job_id" readonly="id"/>
                    <field name="email" widget="email"
                           invisible="not email or not has_domain"/>
                    <button name="create_alias" string="Generate Email" class="btn btn-primary" type="object" invisible="not has_domain or email"/>
                </tree>
            </field>
        </record>

        <record id="hr_recruitment_source_view_search" model="ir.ui.view">
            <field name="name">hr.recruitment.source.view.search</field>
            <field name="model">hr.recruitment.source</field>
            <field name="arch" type="xml">
                <search string="Search Source">
                    <field name="source_id"/>
                    <field name="job_id"/>
                </search>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_hr_job_sources">
            <field name="name">Jobs Sources</field>
            <field name="res_model">hr.recruitment.source</field>
            <field name="view_mode">tree,kanban</field>
            <field name="search_view_id" ref="hr_recruitment_source_view_search"/>
            <field name="context">{'search_default_job_id': [active_id], 'default_job_id': active_id}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Want to analyse where applications come from ?
                </p><p>
                    Use emails and links trackers
                </p>
            </field>
        </record>
    </data>
</odoo>
