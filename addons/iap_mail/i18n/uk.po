# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap_mail
# 
# Translators:
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Company type</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Тип компанії</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Founded</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Знайдено</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies Used</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Технології, що використовуються</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Timezone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Часовий пояс</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Sectors</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Сектори</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Estimated revenue</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Очікуваний дохід</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Phone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Телефон</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>Twitter</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>Twitter</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Employees</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Співробітники</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "<span> per year</span>"
msgstr "<span> на рік</span>"

#. module: iap_mail
#. odoo-javascript
#: code:addons/iap_mail/static/src/js/services/iap_notification_service.js:0
#, python-format
msgid "Buy more credits"
msgstr "Отримати більше кредитів"

#. module: iap_mail
#: model:ir.model,name:iap_mail.model_iap_account
msgid "IAP Account"
msgstr "Рахунок IAP "

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "followers"
msgstr "підписники"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "http://www.twitter.com/"
msgstr "http://www.twitter.com/"
