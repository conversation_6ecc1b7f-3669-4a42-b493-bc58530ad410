# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <juss<PERSON>@gulfeo.com>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <juk<PERSON>.<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2023
# <PERSON> <miu<PERSON>@gmail.com>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>ei<PERSON><PERSON>äjä <<EMAIL>>, 2023
# Martin Trigaux, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#, python-format
msgid " (copy)"
msgstr " (kopio)"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "\"%s\" is not a valid email."
msgstr "\"%s\" ei ole kelvollinen sähköpostiosoite."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "# Messages"
msgstr "# Viestit"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "# arvostelua"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# Istuntoja"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "Keskustelijoiden määrä"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% tyytyväisiä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% Onnellisuudesta"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid ""
"%(new_line)sType %(bold_start)s:shortcut%(bold_end)s to insert a canned "
"response in your message."
msgstr ""
"%(new_line)sKirjoita %(bold_start)s:pikanäppäin%(bold_end)s lisätäksesi "
"valmiiksi laaditun vastauksen viestiin."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%s has joined"
msgstr "%s on liittynyt"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid ""
"'%(input_email)s' does not look like a valid email. Can you please try "
"again?"
msgstr ""
"'%(input_email)s' ei näytä kelvolliselta sähköpostilta. Voisitko yrittää "
"uudelleen?"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Show' displays the chat button on the pages.\n"
"* 'Show with notification' is 'Show' in addition to a floating text just next to the button.\n"
"* 'Open automatically' displays the button and automatically opens the conversation pane.\n"
"* 'Hide' hides the chat button on the pages.\n"
msgstr ""
"* 'Näytä' näyttää chat-painikkeen sivuilla.\n"
"* 'Näytä ilmoituksella' on 'Näytä' ja lisäksi kelluva teksti painikkeen vieressä.\n"
"* 'Avaa automaattisesti' näyttää painikkeen ja avaa automaattisesti keskusteluruudun.\n"
"* 'Piilota' piilottaa keskustelupainikkeen sivuilla.\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ", on"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "1 is Monday, 7 is Sunday"
msgstr "1 on maanantai, 7 on sunnuntai"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "<i class=\"fa fa-mail-reply me-2\" title=\"Reply\"/>"
msgstr "<i class=\"fa fa-mail-reply me-2\" title=\"Vastaa\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"Onnellisten arvioiden "
"prosentti\" role=\"img\" aria-label=\"Hymynaama\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Back to edit mode"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Takaisin muokkaustilaan"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "<i title=\"Remove operator\" class=\"fa fa-fw fa-lg fa-close\"/>"
msgstr "<i title=\"Poista operaattori\" class=\"fa fa-fw fa-lg fa-close\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">Livechat-keskustelu</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>Ystävällisin terveisin,</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>Hei,</span><br/>Tässä on kopio keskustelustasi, jonka kävi"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Reminder: This step will only be played if no operator is "
"available.</span>"
msgstr ""
"<span>Muistutus: Tämä vaihe toistetaan vain, jos operaattoria ei ole "
"saatavilla.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before the Bot can perform more complex actions (Forward to an Operator, ...). </span>\n"
"                    <span>Use Channel Rules if you want the Bot to interact with visitors only when no operator is available.</span>"
msgstr ""
"<span>Vinkki: Vähintään yksi vuorovaikutus (kysymys, sähköposti, ...) tarvitaan, ennen kuin Botti voi suorittaa monimutkaisempia toimintoja (välittää eteenpäin operaattorille, ...). </span>\n"
"                    <span>Käytä kanavasääntöjä, jos haluat botin olevan vuorovaikutuksessa vierailijoiden kanssa vain silloin, kun operaattori ei ole käytettävissä.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before "
"the Bot can perform more complex actions (Forward to an Operator, "
"...).</span>"
msgstr ""
"<span>Vinkki: Vähintään yksi vuorovaikutus (kysymys, sähköposti, ...) "
"tarvitaan, ennen kuin Botti voi suorittaa monimutkaisempia toimintoja "
"(välittää eteenpäin operaattorille, ...).</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Tip: Plan further steps for the Bot in case no operator is "
"available.</span>"
msgstr ""
"<span>Vinkki: Suunnittele botin jatkotoimet siltä varalta, että operaattoria"
" ei ole saatavilla.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<span>You are currently testing</span>"
msgstr "<span>Testaat parhaillaan</span>"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_chatbot_message__unique_mail_message_id
msgid "A mail.message can only be linked to a single chatbot message"
msgstr "Mail.message voidaan yhdistää vain yhteen chatbot-viestiin"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"Istunto on ilman vastausta, jos operaattori ei ole vastannut.\n"
"                                       Jos vierailija on myös operaattori, istuntoon vastataan aina."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__active
msgid "Active"
msgstr "Aktiivinen"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Ylläpitäjä"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "An error occurred. Please try again."
msgstr "Tapahtui virhe. Yritä uudelleen."

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_redirect
msgid "And tadaaaa here you go! 🌟"
msgstr "Ja tadaaaa nyt mennään! 🌟"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Anonymous"
msgstr "Anonyymi"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Nimetön"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__name
msgid "Answer"
msgstr "Vastaus"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__answer_ids
msgid "Answers"
msgstr "Vastaukset"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Oletko matriisin sisällä?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__available_operator_ids
msgid "Available Operator"
msgstr "Käytettävissä oleva operaattori"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg
msgid "Average Rating"
msgstr "Keskimääräinen arvio"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Keskimääräinen arvio (%)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Keskiarvo kestosta"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Viestien keskiarvo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating"
msgstr "Keskiarvoluokitus"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating given by the visitor"
msgstr "Kävijöiden antama keskimääräinen arvosana"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr ""
"Keskimääräinen aika sekunteina ensimmäisen vastauksen antamiseen kävijälle"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Keskimääräinen aika ensimmäiseen vastaukseen vierailijalle"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Bad Ratings"
msgstr "Huonot luokitukset"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__operator_partner_id
msgid "Bot Operator"
msgstr "Bot-operaattori"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "Painikkeen taustaväri"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "Painikkeen tekstin väri"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Tallennetut vastaukset"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Channel"
msgstr "Kanava"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "Kanavan otsikon väri"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel_member
msgid "Channel Member"
msgstr "Kanavan jäsen"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Kanavan nimi"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Kanavan sääntö"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Kanavan säännöt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Kanavan tyyppi"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Channels"
msgstr "Kanavat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Chat-kentän oletusteksti"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Chat on yksityinen ja ainutlaatuinen kahden henkilön välillä. Ryhmä on "
"yksityinen kutsuttujen kesken. Kanavaan voi liittyä vapaasti (sen "
"kokoonpanosta riippuen)."

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.chatbot_script_action
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_script_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Chatbot"
msgstr "Chatbotti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_current_step_id
msgid "Chatbot Current Step"
msgstr "Chatbotin nykyinen vaihe"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_message
msgid "Chatbot Message"
msgstr "Chatbotin viesti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_message_ids
msgid "Chatbot Messages"
msgstr "Chatbotin viestit"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Chatbot Name"
msgstr "Chatbotin nimi"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "Chatbot käsikirjoitus"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_answer
msgid "Chatbot Script Answer"
msgstr "Chatbot käsikirjoitettu vastaus"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "Chatbotin käsikirjoituksen askel"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__script_step_id
msgid "Chatbot Step"
msgstr "Chatbotin vaihe"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.chatbot_config
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Chatbots"
msgstr "Chatbotit"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "Close"
msgstr "Sulje"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Close conversation"
msgstr "Sulje keskustelu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Koodi"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Asetukset"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Configure Channel"
msgstr "Määritä kanava"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Keskustelu"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Conversation ended..."
msgstr "Keskustelu päättyi..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "Keskustelu %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "Käsitellyt keskustelut"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""
"Leikkaa ja liimaa tämä koodi verkkosivustollesi &lt;head&gt; :n sisään:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__country_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
msgid "Country"
msgstr "Maa"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "Kävijän maa"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "Kanavan kävijän maa"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid "Create a Chatbot"
msgstr "Luo chatbot"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "Luo kanava ja aloita keskustelut täyttääkseen historiasi."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Luotu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Luontipäivä"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Luontiaika (tunti)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Asiakasarviot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "Päivän numero"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "Toimintapäivät"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Livechat-painikkeen oletusarvoinen taustaväri"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "Kanavan otsikon oletusarvoinen taustaväri, kun se on avattu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Livechat-painikkeen oletustekstin väri"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Oletusteksti, joka näytetään live-keskustelun tukinapissa"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "Kanavan oletusarvoinen otsikkoväri, kun kanava on avattu"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "Määritä uusi verkkosivuston live chat -kanava"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Define rules for your live support channel. You can apply an action for the "
"given URL, and per country.<br/>To identify the country, GeoIP must be "
"installed on your server, otherwise, the countries of the rule will not be "
"taken into account."
msgstr ""
"Määritä säännöt live-tukikanavalle. Voit soveltaa toimintoa annetulle URL-"
"osoitteelle ja maittain.<br/>Maan tunnistamiseksi GeoIP on asennettava "
"palvelimelle, muuten säännön maita ei oteta huomioon."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Open automatically' otherwise this parameter will "
"not be taken into account."
msgstr ""
"Viive (sekunteina) keskusteluikkunan automaattiselle avaamiselle. Huomautus:"
" valitun toiminnon on oltava \"Avaa automaattisesti\", muuten tätä "
"parametria ei oteta huomioon."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Did we correctly answer your question?"
msgstr "Vastasimmeko oikein kysymykseesi?"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Yhteenveto"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/views/discuss_channel_list/discuss_channel_list_view_controller.js:0
#, python-format
msgid "Discuss"
msgstr "Viestintä"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__discuss_channel_id
msgid "Discussion Channel"
msgstr "Keskustelukanava"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_button.xml:0
#, python-format
msgid "Drag to Move"
msgstr "Siirrä vetämällä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__duration
msgid "Duration"
msgstr "Kesto"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Duration of Session (min)"
msgstr "Istunnon kesto (min)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Keskustelun kesto (sekunneissa)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__duration
msgid "Duration of the session in hours"
msgstr "Istunnon kesto tunteina"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_email
msgid "Email"
msgstr "Sähköposti"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
msgid "Enable the bot only if there is no operator available"
msgstr "Ota botti käyttöön vain, jos operaattoria ei ole saatavilla"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Enabled only if no operator"
msgstr "Käytössä vain, jos operaattoria ei ole"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Explain your note"
msgstr "Merkinnän selitys"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_invalid
msgid "First Step Invalid"
msgstr "Ensimmäinen vaihe virheellinen"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_operator
msgid "First Step Operator"
msgstr "Ensimmäisen askeleen operaattori"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__first_step_warning
msgid "First Step Warning"
msgstr "Ensimmäisen vaiheen varoitus"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""
"Odoo CMS:llä rakennetuilla verkkosivustoilla siirry kohtaan Verkkosivusto "
"&gt; Määritykset &gt; Asetukset ja valitse verkkosivuston live-chat-kanava, "
"jonka haluat lisätä verkkosivustollesi."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__forward_operator
msgid "Forward to Operator"
msgstr "Välittää eteenpäin operaattorille"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_single
msgid "Free Input"
msgstr "Vapaa syöttö"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_multi
msgid "Free Input (Multi-Line)"
msgstr "Vapaa syöttö (monirivinen)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"Annetaan järjestys, jonka mukaan on löydettävä vastaava sääntö. Jos kaksi "
"sääntöä vastaa annettua url-osoitetta/maata, valitaan sääntö, jonka "
"järjestys on alhaisin."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Good Ratings"
msgstr "Hyvät arvosanat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Group By..."
msgstr "Ryhmittele.."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__has_access_livechat
msgid "Has access to Livechat"
msgstr "Pääsy Livechatiin"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr "Onko sinulla kysyttävää? Keskustele kanssamme."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "Otsikon taustaväri"

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr "Hei, kuinka voin auttaa?"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide"
msgstr "Piilota"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "History"
msgstr "Historia"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing
msgid ""
"Hmmm, let me check if I can find someone that could help you with that..."
msgstr ""
"Hmmm, katson, josko löytäisin jonkun, joka voisi auttaa sinua siinä...."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Istunnon aloitustunti"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#, python-format
msgid "How may I help you?"
msgstr "Kuinka voin auttaa?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "Kuinka verkkosivuston live-keskusteluvimpainta käytetään?"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr ""
"Voihan nenä. Näyttää siltä, että yksikään operaattoreistamme ei ole paikalla"
" 🙁"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_just_looking
msgid "I am just looking around"
msgstr "Katselen vain ympärilleni"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_documentation
msgid "I am looking for your documentation"
msgstr "Etsin asiakirjoja"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_pricing
msgid "I have a pricing question"
msgstr "Minulla on kysymys hinnoittelusta"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_exit
msgid "If you need anything else, feel free to get back in touch"
msgstr "Jos tarvitset jotain muuta, ota rohkeasti yhteyttä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1920
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
msgid "Image"
msgstr "Kuva"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1024
msgid "Image 1024"
msgstr "Kuva 1024"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_128
msgid "Image 128"
msgstr "Kuva 128"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_256
msgid "Image 256"
msgstr "Kuva 256"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_512
msgid "Image 512"
msgstr "Kuva 512"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__is_forward_operator_child
msgid "Is Forward Operator Child"
msgstr "On edelleenvälitetty alemman tason operaattorille"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr "Onko luokan livechat auki"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "Onko livechat päällä?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "Onko kävijä anonyymi"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Liity"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Liity kanavalle"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "KPI Livechat-keskustelujen arvo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "KPI Livechat arvio"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "KPI Livechat vastausarvo"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_invitation_patch.xml:0
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
#, python-format
msgid "Lang"
msgstr "Kieli"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Edelliset 24h"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Poissaolo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Poistu kanavalta"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Reaaliaikainen keskustelu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Live Chat Button"
msgstr "Live Chat-painike"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "LiveChat-kanavan haku"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/discuss_app_model_patch.js:0
#: code:addons/im_livechat/static/src/core/web/messaging_menu_patch.js:0
#: code:addons/im_livechat/static/src/core/web/thread_icon_patch.xml:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "Live-tuki"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "Livechat-painike"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Livechat-painikkeen väri"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Live-tukikanava"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__livechat_channel_count
msgid "Livechat Channel Count"
msgstr "Livechat-kanavien määrä"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "Livechat-kanavan säännöt"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__discuss_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Livechat-keskustelu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.qunit_embed_suite
msgid "Livechat External Tests"
msgstr "Livechat Ulkoiset testit"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_lang_ids
msgid "Livechat Languages"
msgstr "Livechat Kielet"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_discuss_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "Livechat-operaattoritunnus vaaditaan livechat-tyyppiselle kanavalle."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "Livechat-tukikanavan raportti"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audience, the duration of a session, etc."
msgstr ""
"Livechat-tukikanavatilastojen avulla voit helposti tarkistaa ja analysoida "
"yrityksesi livechat-istuntojen suorituskykyä. Poimi tiedot unohdetuista "
"istunnoista, yleisöstä, istunnon kestosta jne."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "Livechat-tuen operaattorin raportti"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Livechat-tuen tilastot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_username
msgid "Livechat Username"
msgstr "Livechat käyttäjätunnus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "Livechat-ikkuna"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_lang_ids
msgid "Livechat languages"
msgstr "Livechat kielet"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__livechat_active
msgid "Livechat session is active until visitor leaves the conversation."
msgstr "Livechat-istunto on aktiivinen, kunnes kävijä poistuu keskustelusta."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Vastaava tilaus"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__message
msgid "Message"
msgstr "Viesti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Messages per session"
msgstr "Viestiä istuntoa kohti"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Vastaamattomat istunnot"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "My Sessions"
msgstr "Omat istunnot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__name
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Name"
msgstr "Nimi"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "Yhteistyökumppania ei ole saatavilla, yritä myöhemmin uudelleen."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "Ei vielä asiakasarvioita live chat-istunnosta"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "Ei vielä tietoja!"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "No history found"
msgstr "Historiaa ei löytynyt"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Notification text"
msgstr "Ilmoituksen teksti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__chatbot_script_count
msgid "Number of Chatbot"
msgstr "Chatbottien määrä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
msgid "Number of conversation"
msgstr "Keskusteluiden määrä"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "Päivien lukumäärä operaattorin ensimmäisestä istunnosta lähtien"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Eri puhujien määrä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Keskustelun viestien määrä"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Language"
msgstr "Online-chatin kieli"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "Online Chat-nimi"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Only If"
msgstr "Vain jos"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Open automatically"
msgstr "Avaa automaattisesti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Open automatically timer"
msgstr "Avaa automaattisesti ajastin"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_operator_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Operaattori"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Operaattorien analyysi"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Operaattoria"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Operaattorit, jotka eivät näytä mitään toimintaa Odoossa yli 30 minuuttiin, "
"katsotaan katkaistuksi."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_answer_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "Optional Link"
msgstr "Valinnainen linkki"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Vaihtoehdot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_author_name
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_author_name
msgid "Parent Author Name"
msgstr "Ylemmän tason tekijän nimi"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_body
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_body
msgid "Parent Body"
msgstr "Ylemmän tason viestirunko"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Participant"
msgstr "Osallistuja"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Participants"
msgstr "Ilmoittautuneita yhteensä"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Tyytyväisten arvioiden prosenttiosuus"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_phone
msgid "Phone"
msgstr "Puhelin"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please call me on: "
msgstr "Soita minulle:"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please contact me on: "
msgstr "Ota minuun yhteyttä:"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_just_looking
msgid "Please do! If there is anything we can help with, let us know"
msgstr "Tee ihmeessä niin! Jos voimme auttaa jotenkin, kerro meille"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Järjestelmää pyörittää"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_selection
msgid "Question"
msgstr "Kysymys"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
#, python-format
msgid "Rating"
msgstr "Arvostelu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "Kekismääräisen luokituksen teksti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Arvion viimeisin palaute"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Arvion viimeisin kuva"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Arvion viimeisin arvo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Arvion tyytyväisyys"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_text
msgid "Rating Text"
msgstr "Luokitusteksti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_count
msgid "Rating count"
msgstr "Arvioiden määrä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Ratings"
msgstr "Arviointi"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Arviot livechat-kanavalle"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "Receive a copy of this conversation."
msgstr "Saat kopion tästä keskustelusta."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__redirect_link
msgid "Redirect Link"
msgstr "Ohjauslinkki"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Regexp-lauseke, joka määrittää verkkosivut, joihin tätä sääntöä sovelletaan."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_message_id
msgid "Related Mail Message"
msgstr "Aiheeseen liittyvä sähköpostiviesti"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Replied to"
msgstr "Vastasi aiheeseen"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Raportti"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#, python-format
msgid "Reset to default colors"
msgstr "Palauta oletusvärit"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/thread_actions.js:0
#, python-format
msgid "Restart Conversation"
msgstr "Käynnistä keskustelu uudelleen"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Restarting conversation..."
msgstr "Keskustelun uudelleen aloittaminen..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Säännöt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "Tyytyväisyysaste"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "Tallenna kanavasi saadaksesi määrityswidgetin."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Say something"
msgstr "Sano jotakin"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/composer_patch.js:0
#, python-format
msgid "Say something..."
msgstr "Sano jotain..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Script"
msgstr "Skripti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Käsikirjoitus (ulkoinen)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__script_step_id
msgid "Script Step"
msgstr "Käsikirjoituksen vaihe"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__script_step_ids
msgid "Script Steps"
msgstr "Käsikirjoituksen vaiheet"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Search history"
msgstr "Hakuhistoria"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Hakuraportti"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_commands_patch.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "Katso 15 viimeksi vierailtua sivua"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
#, python-format
msgid "Select an option above"
msgstr "Valitse yllä oleva vaihtoehto"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Send"
msgstr "Lähetä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__sequence
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Session Date"
msgstr "Istunnon päivä"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Session Form"
msgstr "Istunnon lomake"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Istuntojen tilastot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "Istuntoa ei ole luokiteltu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "Istunnot ilman vastausta"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Istunnot"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Istuntojen historia"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Show"
msgstr "Näytä"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Show this step only if all of these answers have been selected."
msgstr "Näytä tämä vaihe vain, jos kaikki nämä vastaukset on valittu."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button_and_text
msgid "Show with notification"
msgstr "Näytä ilmoituksen kanssa"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__source_id
msgid "Source"
msgstr "Lähde"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Istunnon aloituspäivä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "Istunnon aloitustunti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "Vaiheen tyyppi"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/composer_patch.xml:0
#, python-format
msgid "Tab to next livechat"
msgstr "Siirry seuraavaan livechatiin"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__text
msgid "Text"
msgstr "Teksti"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Napin teksti"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "Teksti, joka kehottaa käyttäjää aloittamaan keskustelun."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Text to display on the notification"
msgstr "Ilmoituksessa näytettävä teksti"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
#, python-format
msgid "Thank you for your feedback"
msgstr "Kiitos palautteestasi"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "Säännön kanava"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "The conversation was sent."
msgstr "Keskustelu lähetettiin."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide', the "
"chat button will be hidden on the specified URL from the visitors located in"
" these 2 countries. This feature requires GeoIP installed on your server."
msgstr ""
"Sääntöä sovelletaan vain näihin maihin. Esimerkki: jos valitset 'Belgia' ja "
"'Yhdysvallat' ja asetat toiminnoksi 'Piilota', chat-painike piilotetaan "
"määritetyssä URL-osoitteessa näissä kahdessa maassa sijaitsevilta "
"vierailijoilta. Tämä toiminto edellyttää, että GeoIP on asennettu "
"palvelimellesi."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "Käyttäjä voi poistaa tukikanavia."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Käyttäjä voi liittyä tukikanaviin."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_answer__redirect_link
msgid ""
"The visitor will be redirected to this link upon clicking the option (note "
"that the script will end if the link is external to the livechat website)."
msgstr ""
"Kävijä ohjataan tähän linkkiin, kun hän napsauttaa vaihtoehtoa (huomaa, että"
" skripti loppuu, jos linkki on livechat-sivuston ulkopuolinen)."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "Tällä kanavalla ei ole luokitusta tällä hetkellä"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_lang_ids
msgid ""
"These languages, in addition to your main language, will be used to assign "
"you to Live Chat sessions."
msgstr ""
"Näitä kieliä käytetään pääkielesi lisäksi, kun sinut osoitetaan live-chat-"
"istuntoihin."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "This Month"
msgstr "Kuluva kuukausi"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Tällä viikolla"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Tämä on automaattinen tervetuliaisviesti, jonka kävijäsi näkee, kun hän "
"aloittaa uuden keskustelun."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr "Tätä käyttäjätunnusta käytetään nimenäsi livechat-kanavissa."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Vastausaika"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "Vastausaika (sek)"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "Vihje: Käytä valmiita vastauksia nopeuttaaksesi keskustelua"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__title
msgid "Title"
msgstr "Otsikko"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "Otsikon väri"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Vastatut istunnot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"URL-osoite staattiselle sivulle, jossa asiakas voi keskustella kanavan "
"ylläpitäjän kanssa."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Unrated"
msgstr "Arvostelematon"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""
"Käytä valmiita vastauksia määritelläksesi viestimallit livechat-"
"sovelluksessa. Jos haluat ladata valmiiksi tallennetun vastauksen, aloita "
"lause ':'-merkillä ja valitse malli."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Käyttäjä"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "Käyttäjän Livechat Käyttäjätunnus"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "Käyttäjäasetukset"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_script_answer_id
msgid "User's answer"
msgstr "Käyttäjän vastaus"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_raw_answer
msgid "User's raw answer"
msgstr "Käyttäjän raaka vastaus"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
#, python-format
msgid "Visitor"
msgstr "Vierailija"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "Vierailija on onnellinen"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
#, python-format
msgid "Visitor left the conversation."
msgstr "Vierailija poistui keskustelusta."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Verkkosivu"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Verkkosivuston live-tukikanavat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: im_livechat
#: model:chatbot.script,title:im_livechat.chatbot_script_welcome_bot
msgid "Welcome Bot"
msgstr "Tervetuloa Bot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Tervetuloviesti"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_welcome
msgid "Welcome to CompanyName! 👋"
msgstr "Tervetuloa CompanyNameen! 👋"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_dispatch
msgid "What are you looking for?"
msgstr "Mitä olet etsimässä?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Sovelma"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr "Jättäisitkö sähköpostiosoitteesi jotta voimme ottaa sinuun yhteyttä?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "You"
msgstr "Sinä"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload attachments on this channel."
msgstr "Tällä kanavalla ei saa ladata liitetiedostoja."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid ""
"You can create a new Chatbot with a defined script to speak to your website "
"visitors."
msgstr ""
"Voit luoda uuden Chatbotin, jossa on määritelty skripti, joka puhuu "
"verkkosivustosi kävijöille."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"Voit luoda kanavia jokaiselle verkkosivustolle, jolla haluat olla\n"
"                integroida verkkosivuston live-chat-widgetin, jolloin verkkosivustosi\n"
"                kävijät voivat keskustella reaaliaikaisesti operaattoreidesi kanssa."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Your chatter history is empty"
msgstr "Keskusteluhistoriasi on tyhjä"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
#, python-format
msgid "country"
msgstr "maa"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "e.g. \"Meeting Scheduler Bot\""
msgstr "esim. \"Aikatauluavustaja-botti\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "e.g. 'How can I help you?'"
msgstr "esim. \"Miten voin auttaa?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "esim. /contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "esim. Hei, miten voi auttaa?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "esim. OmatSivut.fi"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"tai kopioi tämä osoite ja lähetä se sähköpostitse asiakkaille ja "
"toimittajille:"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "sekuntia"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
