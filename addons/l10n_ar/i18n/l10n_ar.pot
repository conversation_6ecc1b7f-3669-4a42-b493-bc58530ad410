# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ar
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-12 13:15+0000\n"
"PO-Revision-Date: 2025-03-12 13:15+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
msgid ""
"(AR) Exento undertakes to do its best to supply performant services in due "
"time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. "
"(AR) Exento cannot under any circumstances, be required by the client to "
"appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
msgid ""
"(AR) Monotributista undertakes to do its best to supply performant services "
"in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. "
"(AR) Monotributista cannot under any circumstances, be required by the "
"client to appear as a third party in the context of any claim for damages "
"filed against the client by an end consumer."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"(AR) Responsable Inscripto undertakes to do its best to supply performant "
"services in due time in accordance with the agreed timeframes. However, none"
" of its obligations can be considered as being an obligation to achieve "
"results. (AR) Responsable Inscripto cannot under any circumstances, be "
"required by the client to appear as a third party in the context of any "
"claim for damages filed against the client by an end consumer."
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "- Activities Start:"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "- CUIT:"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__01
msgid "01 - National Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__02
msgid "02 - Provincial Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__03
msgid "03 - Municipal Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__04
msgid "04 - Internal Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__06
msgid "06 - VAT perception"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__07
msgid "07 - IIBB perception"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__08
msgid "08 - Municipal Taxes Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__09
msgid "09 - Other Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__4
msgid "10.5%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__9
msgid "2,5%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__5
msgid "21%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__6
msgid "27%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__99
msgid "99 - Others"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid ""
"<br/>\n"
"                        <strong>Incoterm:</strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>CBU for payment: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Currency: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Exchange rate: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Payment Terms: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Reference:</strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Source:</strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>\n"
"                Describe something that made you proud, a piece of work positive for\n"
"                the company.\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>\n"
"                Did you face new difficulties? Did you confront yourself to new\n"
"                obstacles?\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>\n"
"                Every job has strong points, what are, in your opinion, the tasks that\n"
"                you enjoy the most/the least?\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"<em>\n"
"                From a manager point of view, how could you help the employee to\n"
"                overcome their weaknesses?\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>\n"
"                How can the company help you with your need and objectives in order\n"
"                for you to reach your goals and look for the best collaboration.\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"<em>\n"
"                How do you see the employee in the future, do your vision follow the\n"
"                employee's desire?\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"<em>\n"
"                Some achievements comforting you in their strengths to face job's\n"
"                issues.\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "<em>Autonomy</em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>Culture/Behavior:</em>\n"
"                <span class=\"o_stars o_five_stars\" id=\"checkId-1\">\n"
"                    <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "<em>Do you need rapid answer to the current situation?</em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid "<em>Give an example of long-term objective (&gt; 6 months)</em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>Give an example of short-term objective (\n"
"            </em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>Internal Communication:</em>\n"
"                <span class=\"o_stars o_five_stars\" id=\"checkId-2\">\n"
"                    <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>Job's content:</em>\n"
"                <span class=\"o_stars o_five_stars\" id=\"checkId-3\">\n"
"                    <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "<em>Pro-activity</em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>Remuneration:</em>\n"
"                <span class=\"o_stars o_five_stars\" id=\"checkId-5\">\n"
"                    <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "<em>Stress Resistance</em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "<em>Teamwork</em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "<em>Time Management</em>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<em>Work organization:</em>\n"
"                <span class=\"o_stars o_five_stars\" id=\"checkId-4\">\n"
"                    <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                Evaluation\n"
"            </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                Feedback\n"
"            </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                Improvements\n"
"            </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                My feelings\n"
"            </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                My future\n"
"            </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                My work\n"
"            </span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span>% VAT</span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span>Amount</span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span>NCM</span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Customer: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Supplier: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Due Date: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Invoiced period: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Son: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>VAT Cond: </strong>"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_concept
#: model:ir.model.fields,help:l10n_ar.field_account_move__l10n_ar_afip_concept
#: model:ir.model.fields,help:l10n_ar.field_account_payment__l10n_ar_afip_concept
msgid ""
"A concept is suggested regarding the type of the products on the invoice but"
" it is allowed to force a different type if required."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"A required tax group could not be found (XML ID: %s).\n"
"Please reload your chart template in order to reinstall the required tax group.\n"
"\n"
"Note: You might have to relink your existing taxes to this new tax group."
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_ac_dis_cf
msgid "ACCOUNTING ADJUSTMENTS THAT DECREASE THE TAX CREDIT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_ac_inc_cf
msgid "ACCOUNTING ADJUSTMENTS THAT INCREASE TAX CREDITS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_ac_inc_df
msgid "ACCOUNTING ADJUSTMENTS THAT INCREASE TAX LIABILITIES"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_ac_dis_df
msgid "ACCOUNTING ADJUSTMENTS THAT REDUCE TAX LIABILITIES"
msgstr ""

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.menu_afip_config
msgid "AFIP"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency__l10n_ar_afip_code
msgid "AFIP Code"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_concept
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_concept
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_concept
msgid "AFIP Concept"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_partner_id
msgid "AFIP POS Address"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_number
msgid "AFIP POS Number"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_system
msgid "AFIP POS System"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_ar_afip_responsibility_type
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_afip_responsibility_type_id
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_afip_responsibility_type_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_afip_responsibility_type_tree
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_res_partner_filter
msgid "AFIP Responsibility Type"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_afip_responsibility_type
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position__l10n_ar_afip_responsibility_type_ids
msgid "AFIP Responsibility Types"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_service_end
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_service_end
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_service_end
msgid "AFIP Service End Date"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_service_start
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_service_start
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_service_start
msgid "AFIP Service Start Date"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_AN
msgid "AN"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_aa_dj_neg
msgid "ANNUAL ADJUSTMENT ARISING FROM THE NEGATIVE VAT D J"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_aa_dj_pos
msgid "ANNUAL ADJUSTMENT ARISING FROM THE POSITIVE VAT D J"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nca
msgid "ASSIGNMENT CREDIT NOTE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_na
msgid "ASSIGNMENT NOTE"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "Account"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__date
msgid "Accounting Date"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "Accounting Date: This Year"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Accounting Documents"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Accounting Settings"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__active
msgid "Active"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_afip_start_date
msgid "Activities Start"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"All our contractual relations will be governed exclusively by Argentina law."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_is_pos
msgid ""
"Argentina: Specify if this Journal will be used to send electronic invoices "
"to AFIP."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_afip_pos_system
msgid ""
"Argentina: Specify which type of system will be used to create the "
"electronic invoice. This will depend on the type of invoice to be created."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_uom_uom__l10n_ar_afip_code
msgid "Argentina: This code will be used on electronic invoice."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_gross_income_type
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_gross_income_type
msgid "Argentina: Type of gross income: exempt, local, multilateral."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/template_ar_ex.py:0
#, python-format
msgid "Argentine Generic Chart of Accounts for Exempt Individuals"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/template_ar_ri.py:0
#, python-format
msgid "Argentine Generic Chart of Accounts for Registered Accountants"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_document_type_filter
msgid "Argentinean Documents"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.res_config_settings_view_form
msgid "Argentinean Localization"
msgstr ""

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.account_reports_ar_statements_menu
msgid "Argentinean Statements"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_AN
msgid "Birth certificate"
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_exento_product_template
msgid "Book \"Development in Odoo\" (VAT Exempt)"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_nvc
msgid "CASH SALES NOTES A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_nvc
msgid "CASH SALES NOTES B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_nvc
msgid "CASH SALES NOTES C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_nvc
msgid "CASH SALES NOTES M"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CBA
msgid "CBA"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner_bank.py:0
#, python-format
msgid "CBU"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCat
msgid "CCat"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCor
msgid "CCor"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCorr
msgid "CCorr"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CDI
#: model:l10n_latam.identification.type,name:l10n_ar.it_CDI
msgid "CDI"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIBAR
msgid "CI Bs. As. RNP"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CBA
msgid "CI Buenos Aires"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CCat
msgid "CI Catamarca"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CICha
msgid "CI Chaco"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIChu
msgid "CI Chubut"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CCorr
msgid "CI Corrientes"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CCor
msgid "CI Córdoba"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIER
msgid "CI Entre Ríos"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CPF
msgid "CI Federal Police"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIF
msgid "CI Formosa"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIJ
msgid "CI Jujuy"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CILP
msgid "CI La Pampa"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CILR
msgid "CI La Rioja"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIMen
msgid "CI Mendoza"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIMis
msgid "CI Misiones"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIN
msgid "CI Neuquén"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIRN
msgid "CI Río Negro"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIS
msgid "CI Salta"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CISJ
msgid "CI San Juan"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CISL
msgid "CI San Luis"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CISC
msgid "CI Santa Cruz"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CISF
msgid "CI Santa Fe"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CISdE
msgid "CI Santiago del Estero"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CITdF
msgid "CI Tierra del Fuego"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CIT
msgid "CI Tucumán"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIBAR
msgid "CIBAR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CICha
msgid "CICha"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIChu
msgid "CIChu"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIER
msgid "CIER"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIF
msgid "CIF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIJ
msgid "CIJ"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CILP
msgid "CILP"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CILR
msgid "CILR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIMen
msgid "CIMen"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIMis
msgid "CIMis"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIN
msgid "CIN"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIRN
msgid "CIRN"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIS
msgid "CIS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISC
msgid "CISC"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISF
msgid "CISF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISJ
msgid "CISJ"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISL
msgid "CISL"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISdE
msgid "CISdE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIT
msgid "CIT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CITdF
msgid "CITdF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_uci_a
msgid "CLASS A SINGLE COMMERCIAL TAX SETTLEMENT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_uci_b
msgid "CLASS B SINGLE COMMERCIAL TAX SETTLEMENT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_uci_c
msgid "CLASS C SINGLE COMMERCIAL TAX SETTLEMENT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CPF
msgid "CPF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_rfc
msgid "CREDIT INVOICE RECEIPTS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_a_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_b_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_c_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_e_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_m_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nc_m
msgid "CREDIT NOTE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_sp_nc
msgid "CREDIT NOTE PUBLIC SERVICES/TAX CONTROLLERS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nc_liq_uci_c
msgid "CREDIT NOTE SINGLE COMMERCIAL TAX SETTLEMENT CLASS A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nc_liq_uci_a
msgid "CREDIT NOTE SINGLE COMMERCIAL TAX SETTLEMENT CLASS B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nc_liq_uci_b
msgid "CREDIT NOTE SINGLE COMMERCIAL TAX SETTLEMENT CLASS C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nc_a
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nc_b
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nc_c
msgid "CREDIT NOTE TICKET"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nc_a
msgid "CREDIT NOTE TICKET A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nc_b
msgid "CREDIT NOTE TICKET B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nc_c
msgid "CREDIT NOTE TICKET C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nc_m
msgid "CREDIT NOTE TICKET M"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_nc
msgid "CREDIT NOTES A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_nc
msgid "CREDIT NOTES B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_nc
msgid "CREDIT NOTES C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_e_nc
msgid "CREDIT NOTES FOR FOREIGN OPERATIONS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_nc
msgid "CREDIT NOTES M"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nc_rg1415
msgid "CREDIT NOTES OR EQUIVALENT DOCUMENT COMPLYING WITH G.R. NO. 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CUIL
msgid "CUIL"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_cuit
msgid "CUIT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_aduana
msgid "CUSTOMS DOCUMENT"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid ""
"Can not create chart of account until you configure your company AFIP "
"Responsibility and VAT."
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CdM
msgid "CdM"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can "
"(AR) Exento become involved in costs related to a country's legislation. The"
" amount of the invoice will therefore be due to (AR) Exento in its entirety "
"and does not include any costs relating to the legislation of the country in"
" which the client is located."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can "
"(AR) Monotributista become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to (AR) "
"Monotributista in its entirety and does not include any costs relating to "
"the legislation of the country in which the client is located."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can "
"(AR) Responsable Inscripto become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to (AR) "
"Responsable Inscripto in its entirety and does not include any costs "
"relating to the legislation of the country in which the client is located."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__code
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom__l10n_ar_afip_code
msgid "Code"
msgstr ""

#. module: l10n_ar
#: model:ir.model.constraint,message:l10n_ar.constraint_l10n_ar_afip_responsibility_type_code
msgid "Code must be unique!"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_company_requires_vat
msgid "Company Requires Vat?"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_vat
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_vat
msgid ""
"Computed field that returns VAT or nothing if this one is not set for the "
"partner"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_formatted_vat
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_formatted_vat
msgid ""
"Computed field that will convert the given VAT number to the format "
"{person_category:2}-{number:10}-{validation_number:1}"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_company.py:0
#, python-format
msgid ""
"Could not change the AFIP Responsibility of this company because there are "
"already accounting entries."
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_country
msgid "Country"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_currency
msgid "Currency"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_currency_rate
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_currency_rate
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_currency_rate
msgid "Currency Rate"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_zeta
msgid "DAILY CLOSING REPORT (ZETA) - FISCAL CONTROLLERS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_oc_nd
msgid "DATA OVERVIEW"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_nd
msgid "DEBIT MEMOS A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_nd
msgid "DEBIT MEMOS B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_nd
msgid "DEBIT MEMOS M"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_a_nd
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_b_nd
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_c_nd
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_e_nd
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_m_nd
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nd_m
msgid "DEBIT NOTE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nd_liq_uci_a
msgid "DEBIT NOTE SINGLE COMMERCIAL TAX SETTLEMENT CLASS A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nd_liq_uci_b
msgid "DEBIT NOTE SINGLE COMMERCIAL TAX SETTLEMENT CLASS B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nd_liq_uci_c
msgid "DEBIT NOTE SINGLE COMMERCIAL TAX SETTLEMENT CLASS C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nd_a
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nd_b
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_nd_c
msgid "DEBIT NOTE TICKET"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nd_a
msgid "DEBIT NOTE TICKET A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nd_b
msgid "DEBIT NOTE TICKET B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nd_c
msgid "DEBIT NOTE TICKET C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_nd_m
msgid "DEBIT NOTE TICKET M"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_nd
msgid "DEBIT NOTES C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_e_nd
msgid "DEBIT NOTES FOR FOREIGN OPERATIONS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nd_rg1415
msgid "DEBIT NOTES OR EQUIVALENT DOCUMENT COMPLYING WITH G.R. NO. 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cd_sp_a
msgid "DIRECT PURCHASE LIQUIDATION A - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cd_sa_a
msgid "DIRECT PURCHASE LIQUIDATION A - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cd_sp_b
msgid "DIRECT PURCHASE LIQUIDATION B - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cd_sa_b
msgid "DIRECT PURCHASE LIQUIDATION B - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cd_sp_c
msgid "DIRECT PURCHASE LIQUIDATION C - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cd_sa_c
msgid "DIRECT PURCHASE LIQUIDATION C - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_dni
msgid "DNI"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Date:"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_account_move__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_account_payment__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_afip_responsibility_type_id
msgid ""
"Defined by AFIP to identify the type of responsibilities that a person or a "
"legal entity could have and that impacts in the type of operations and "
"requirements they need."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__l10n_ar_state_id
msgid "Delivery Province"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_document_type_filter
msgid "Document Letter"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_document_type_argentina
#: model:ir.ui.menu,name:l10n_ar.menu_document_type_argentina
msgid "Document Types"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cert_ele_gr
msgid "ELECTRONIC CERTIFICATION (GRAINS)"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_a_f
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_b_f
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_c_f
msgid "ELECTRONIC CREDIT INVOICE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_b_f
msgid "ELECTRONIC CREDIT INVOICE FOR SMBs (ECF) B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_c_f
msgid "ELECTRONIC CREDIT INVOICE FOR SMBs (FCE) C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_a_f
msgid "ELECTRONIC CREDIT INVOICE FOR SMBs (FCE) TO"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_a_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_b_nc
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_c_nc
msgid "ELECTRONIC CREDIT NOTE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_a_nc
msgid "ELECTRONIC CREDIT NOTE SME SMEs (FCE) A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_c_nc
msgid "ELECTRONIC CREDIT NOTE SME's (FCE) C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_b_nc
msgid "ELECTRONIC CREDIT NOTICE FOR SMBs (FCE) B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_a_nd
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_b_nd
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_fce_c_nd
msgid "ELECTRONIC DEBIT MEMO"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_b_nd
msgid "ELECTRONIC DEBIT NOTE SME's (FCE) B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_c_nd
msgid "ELECTRONIC DEBIT NOTE SME's (FCE) C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_fce_a_nd
msgid "ELECTRONIC DEBIT NOTE SMMEs (FCE) A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_oc_c
msgid "ELECTRONIC MAILING"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_ET
msgid "ET"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_e_f
msgid "EXPORT INVOICES"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Electronic Fiscal Bond - Online Invoice"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__2
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__exempt
msgid "Exempt"
msgstr ""

#. module: l10n_ar
#: model:account.journal,name:l10n_ar.sale_expo_journal_exento
#: model:account.journal,name:l10n_ar.sale_expo_journal_mono
#: model:account.journal,name:l10n_ar.sale_expo_journal_ri
msgid "Expo Sales Journal"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Export Voucher - Billing Plus"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Export Voucher - Online Invoice"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.nc_exterior
msgid "FOREIGN CREDIT NOTES AND REIMBURSEMENTS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_f1116b
msgid "FORM 1116 B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_f1116c
msgid "FORM 1116 C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_f1116
msgid "FORM 1116 RT"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "Fiscal Transparency Regime for the Final Consumer (Law 27.743)"
msgstr ""

#. module: l10n_ar
#: model:account.account,name:l10n_ar.3_base_fondo_comun_de_inversion
#: model:account.account,name:l10n_ar.4_base_fondo_comun_de_inversion
#: model:account.account,name:l10n_ar.5_base_fondo_comun_de_inversion
#: model:account.account.template,name:l10n_ar.base_fondo_comun_de_inversion
msgid "Fondo común de inversión"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_formatted_vat
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_formatted_vat
msgid "Formatted VAT"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/template_ar_base.py:0
#, python-format
msgid "Generic Chart of Accounts Argentina Single Taxpayer / Basis"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_legal_entity_vat
msgid ""
"Generic VAT number defined by AFIP in order to recognize partners from this "
"country that are legal entity"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_natural_vat
msgid ""
"Generic VAT number defined by AFIP in order to recognize partners from this "
"country that are natural persons"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_other_vat
msgid ""
"Generic VAT number defined by AFIP in order to recognize partners from this "
"country that are not natural persons or legal entities"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"Give one positive achievement that convinced you of the employee's\n"
"            value."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Go to Companies"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Go to Journals"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_gross_income_type
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_company_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Gross Income"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_gross_income_number
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_gross_income_number
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_gross_income_number
msgid "Gross Income Number"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_gross_income_type
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_gross_income_type
msgid "Gross Income Type"
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_servicio_de_guarda_product_template
msgid "Guard Service"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "How could the employee improve?"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid "How do I feel about my own..."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid "How do I feel about the company..."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__id
msgid "ID"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_iibb_purchases_by_state_and_account_pivot
#: model:ir.ui.menu,name:l10n_ar.menu_iibb_purchases_by_state_and_account
msgid "IIBB - Purchases by jurisdiction"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_iibb_sales_by_state_and_account_pivot
#: model:ir.ui.menu,name:l10n_ar.menu_iibb_sales_by_state_and_account
msgid "IIBB - Sales by jurisdiction"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "IIBB:"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_desp_imp
msgid "IMPORT CLEARANCE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_imp_serv
msgid "IMPORT OF SERVICES"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_a_f
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_b_f
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_c_f
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_e_f
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_m_f
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_m
msgid "INVOICE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_a_t
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_b_t
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t_c
msgid "INVOICE TICKET"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_e_fs
msgid "INVOICES - SIMPLIFIED EXPORT PERMIT - DTO. 855/97"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_f
msgid "INVOICES A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.fa_exterior
msgid "INVOICES AND RECEIPTS FROM ABROAD"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_f
msgid "INVOICES B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_f
msgid "INVOICES C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_f
msgid "INVOICES M"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_latam_identification_type
msgid "Identification Types"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, (AR) Exento reserves the right to call on the services of a "
"debt recovery company. All legal expenses will be payable by the client."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, (AR) Monotributista reserves the right to call on the services"
" of a debt recovery company. All legal expenses will be payable by the "
"client."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, (AR) Responsable Inscripto reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_quote_despacho_product_template
msgid "Import Clearance"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
msgid ""
"In order for it to be admissible, (AR) Exento must be notified of any claim "
"by means of a letter sent by recorded delivery to its registered office "
"within 8 days of the delivery of the goods or the provision of the services."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
msgid ""
"In order for it to be admissible, (AR) Monotributista must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"In order for it to be admissible, (AR) Responsable Inscripto must be "
"notified of any claim by means of a letter sent by recorded delivery to its "
"registered office within 8 days of the delivery of the goods or the "
"provision of the services."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner_bank.py:0
#: code:addons/l10n_ar/models/res_partner_bank.py:0
#, python-format
msgid "Invalid Checksum"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner_bank.py:0
#, python-format
msgid "Invalid Format"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner_bank.py:0
#, python-format
msgid "Invalid Length"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "Invalid length for \"%s\""
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_is_pos
msgid "Is AFIP POS?"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_LC
#: model:l10n_latam.identification.type,name:l10n_ar.it_LC
msgid "LC"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_LE
#: model:l10n_latam.identification.type,name:l10n_ar.it_LE
msgid "LE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_a_cvl
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_b_cvl
msgid "LIQUID PRODUCT SALES ACCOUNT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_a_l
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_b_l
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_c_l
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_m_l
msgid "LIQUIDATION"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_l
msgid "LIQUIDATION C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cpc_a
msgid "LIQUIDATION OF BREEDING BROILER CHICKENS A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cpc_b
msgid "LIQUIDATION OF BREEDING BROILER CHICKENS B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_s_a
msgid "LIQUIDATION OF CLASS A UTILITIES"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_s_b
msgid "LIQUIDATION OF CLASS B UTILITIES"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_vd_sp_a
msgid "LIQUIDATION OF DIRECT SALE A - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_vd_sa_a
msgid "LIQUIDATION OF DIRECT SALES A - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_vd_sp_b
msgid "LIQUIDATION OF DIRECT SALES B - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_vd_sa_b
msgid "LIQUIDATION OF DIRECT SALES B - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_ccpc_a
msgid "LIQUIDATION OF HIRING OF BREEDING GRILL CHICKENS A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_ccpc_b
msgid "LIQUIDATION OF HIRING OF BREEDING GRILL CHICKENS B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_ccpc_c
msgid "LIQUIDATION OF HIRING OF BREEDING GRILL CHICKENS C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cpst_a
msgid "LIQUIDATION OF PRIMARY PURCHASE FOR THE TOBACCO SECTOR A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_l
msgid "LIQUIDATIONS A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_l
msgid "LIQUIDATIONS B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_l
msgid "LIQUIDATIONS M"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_legal_entity_vat
msgid "Legal Entity VAT"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__l10n_ar_letter
msgid "Letters"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_l10n_latam_document_type__l10n_ar_letter
msgid ""
"Letters defined by the AFIP that can be used to identify the documents "
"presented to the government and that depends on the operation type, the "
"responsibility of both the issuer and the receptor of the document"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_fiscal_position__l10n_ar_afip_responsibility_type_ids
msgid ""
"List of AFIP responsibilities where this fiscal position should be auto-"
"detected"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__local
msgid "Local"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Logo"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid ""
"Long term (&gt; 6 months) career discussion, where does the employee\n"
"            wants to go, how to help them reach this path?"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_remito_x
msgid "MAILING"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_r_r
msgid "MAILING R"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_remito_x
msgid "MAILING X"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_mandato
msgid "MANDATE - CONSIGNMENT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CdM
msgid "Migration Certificate"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Missing Partner Configuration"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__multilateral
msgid "Multilateral"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__name
msgid "Name"
msgstr ""

#. module: l10n_ar
#: model:ir.model.constraint,message:l10n_ar.constraint_l10n_ar_afip_responsibility_type_name
msgid "Name must be unique!"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_dni
msgid "National Identity Card"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_natural_vat
msgid "Natural Person VAT"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "No VAT configured for partner [%i] %s"
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_cero_product_template
msgid "Non-industrialized animals and vegetables (VAT Zero)"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__0
msgid "Not Applicable"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__l10n_latam_document_type__purchase_aliquots__not_zero
msgid "Not Zero"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Nro:"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_company_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Number..."
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_o_rg1415
msgid "OTHER COMPROBANTES A QUE CUMPLEN CON LA R.G. Nº 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_o_rg1415
msgid "OTHER COMPROBANTES B QUE CUMPLAN CON LA R.G. Nº 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_o_rg1415
msgid "OTHER COMPROBANTES C QUE CUMPLAN CON LA R.G. Nº 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_o_rg1415
msgid "OTHER COMPROBANTES M QUE CUMPLAN CON LA R.G. Nº 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_oc_nc
msgid "OTHER VOUCHERS - EXCEPTED DOCUMENTS - CREDIT NOTES"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_oc_se
msgid "OTHER VOUCHERS - FOREIGN SERVICES"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_oc_ncrg3419
msgid ""
"OTHER VOUCHERS THAT DO NOT COMPLY WITH OR ARE EXEMPT FROM G.R. NO. 1415 AND "
"AMENDMENTS THERETO"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "On invoice id %r you must use VAT Not Applicable on every line."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"On invoice id %r you must use VAT taxes different than VAT Not Applicable."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Online Invoice"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "Only numbers allowed for \"%s\""
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Other National Ind. Taxes %s"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_otras_percepciones
msgid "Other Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_special_purchase_document_type_ids
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_special_purchase_document_type_ids
msgid "Other Purchase Documents"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_other_vat
msgid "Other VAT"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, (AR) Exento reserves the right to request a "
"fixed interest payment amounting to 10% of the sum remaining due. (AR) "
"Exento will be authorized to suspend any provision of services without prior"
" warning in the event of late payment."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, (AR) Monotributista reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"(AR) Monotributista will be authorized to suspend any provision of services "
"without prior warning in the event of late payment."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, (AR) Responsable Inscripto reserves the right "
"to request a fixed interest payment amounting to 10% of the sum remaining "
"due. (AR) Responsable Inscripto will be authorized to suspend any provision "
"of services without prior warning in the event of late payment."
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_con_a_m
msgid "PRIMARY CONSIGNMENT \"A\" VOUCHERS FOR THE MARINE FISHING SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_con_b_m
msgid "PRIMARY CONSIGNMENT \"B\" VOUCHERS FOR THE MARINE FISHING SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_prim_gr
msgid "PRIMARY GRAIN SETTLEMENT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_com_a_m
msgid "PRIMARY PURCHASE \"A\" VOUCHERS FOR THE MARINE FISHING SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_com_b_m
msgid "PRIMARY PURCHASE \"B\" VOUCHERS FOR THE MARINE FISHING SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cpst_b
msgid "PRIMARY PURCHASE LIQUIDATION FOR THE TOBACCO SECTOR B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_nc
msgid "PROOF CREDIT NOTES WITH CODE. 34, 39, 58, 59, 60, 63, 96, 97,"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_usados
msgid "PROOF OF PURCHASE OF USED GOODS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_bs_no_reg
msgid "PROOFS OF PURCHASE OF NON-REGISTRABLE GOODS TO FINAL CONSUMERS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_c_sp_a
msgid "PURCHASE LIQUIDATION A - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_c_sa_a
msgid "PURCHASE LIQUIDATION A - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_c_sp_b
msgid "PURCHASE LIQUIDATION B - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_c_sa_b
msgid "PURCHASE LIQUIDATION B - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__company_partner
msgid "Partner"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Please configure the AFIP Responsibility for \"%s\" in order to continue"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Please define a valid AFIP POS number (5 digits max)"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Please define an AFIP POS number"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Pre-printed Invoice"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Product Coding - Online Voucher"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__purchase_aliquots
msgid "Purchase Aliquots"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_a_r
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_b_r
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_c_r
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_m_r
msgid "RECEIPT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_r
msgid "RECEIPT A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_r
msgid "RECEIPT B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_r
msgid "RECEIPT C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_rf
msgid "RECEIPT OF INVOICE UNDER THE CREDIT INVOICE SYSTEM"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_reciclado
msgid "RECEIPTS FOR RECYCLING MATERIALS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_r
msgid "RECEIPTS M"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_l10n_latam_document_type__purchase_aliquots
msgid ""
"Raise an error if a vendor bill is miss encoded. \"Not Zero\" means the VAT "
"taxes are required for the invoices related to this document type, and those"
" with \"Zero\" means that only \"VAT Not Applicable\" tax is allowed."
msgstr ""

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.menu_afip_responsibility_type
msgid "Responsibility Types"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_cvl
msgid "SALES ACCOUNTS AND LIQUID PRODUCT A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_cvl
msgid "SALES ACCOUNTS AND LIQUID PRODUCT B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_cvl
msgid "SALES ACCOUNTS AND LIQUID PRODUCT M"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cvl_sp_a
msgid "SALES AND CASH ACCOUNT PRODUCT A - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cvl_sa_a
msgid "SALES AND CASH ACCOUNT PRODUCT A - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cvl_sp_b
msgid "SALES AND CASH ACCOUNT PRODUCT B - LIVESTOCK SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cvl_sa_b
msgid "SALES AND CASH ACCOUNT PRODUCT B - POULTRY SECTOR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_sec_gr
msgid "SECONDARY GRAIN LIQUIDATION"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cca_a
#: model:l10n_latam.document.type,name:l10n_ar.dc_liq_cca_b
msgid "SUGAR CANE PURCHASE LIQUIDATION A"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__sequence
msgid "Sequence"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "Service Date"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_manager_feedback_template:l10n_ar.company_ri
msgid "Short term (6-months) actions / decisions / objectives"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_Sigd
msgid "Sigd"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "State"
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_tasa_estadistica_product_template
msgid "Statistics Rate"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cfcp
msgid "TAX CREDIT FOR EMPLOYER CONTRIBUTIONS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_t
msgid "TICKET"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_t
msgid "TICKET - INVOICE B"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_t
msgid "TICKET INVOICE A"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_c
msgid "TICKET INVOICE C"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_t_m
msgid "TICKET INVOICE M"
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_arancel_product_template
msgid "Tariff"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_tax_group
msgid "Tax Group"
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_telefonia_product_template
msgid "Telephone service (VAT 27)"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,sign_terms:l10n_ar.company_exento
#: model_terms:res.company,sign_terms:l10n_ar.company_mono
#: model_terms:res.company,sign_terms:l10n_ar.company_ri
#: model_terms:res.company,sign_terms_html:l10n_ar.company_exento
#: model_terms:res.company,sign_terms_html:l10n_ar.company_mono
#: model_terms:res.company,sign_terms_html:l10n_ar.company_ri
msgid "Terms &amp; Conditions"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"The document number can not be changed for this journal, you can only modify"
" the POS number if there is not posted (or posted before) invoices"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid ""
"The document number must be entered with a dash (-) and a maximum of 5 characters for the first partand 8 for the second. The following are examples of valid numbers:\n"
"* 1-1\n"
"* 0001-********\n"
"* 00001-********"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid "The number of import Dispatch must be 16 characters"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "The pos system %s can not be used on a purchase journal (id %s)"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"The selected Journal can't be used in this transaction, please select one "
"that doesn't use documents as these are just for Invoices."
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid ""
"The tax credit specified in this voucher may only be computed for purposes "
"of the Tax Support and Inclusion Regime for Small Taxpayers of Law No. "
"27,618."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_tax_group.py:0
#, python-format
msgid ""
"The tax group '%s' can't be removed, since it is required in the Argentinian"
" localization."
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "The validation digit is not valid for \"%s\""
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"There should be a single tax from the \"VAT\" tax group per line, add it to "
"%r. If you already have it, please check the tax configuration, in advanced "
"options, in the corresponding field \"Tax Group\"."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_afip_code
#: model:ir.model.fields,help:l10n_ar.field_res_currency__l10n_ar_afip_code
msgid "This code will be used on electronic invoice"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_gross_income_number
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_gross_income_type
msgid "This field is required in order to print the invoice report properly"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_special_purchase_document_type_ids
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_special_purchase_document_type_ids
msgid ""
"This field will be deprecated in the next version as it is no longer needed."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_afip_pos_partner_id
msgid "This is the address used for invoice reports of this POS"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_afip_pos_number
msgid ""
"This is the point of sale number assigned by AFIP in order to generate "
"invoices"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__l10n_ar_tribute_afip_code
msgid "Tribute AFIP Code"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_sp_c
msgid "UTILITY BILL VOUCHER FINANCIAL INTEREST"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_sp_nd
msgid "UTILITY DEBIT MEMO"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_Sigd
msgid "Unidentified/global daily sales"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_CUIL
msgid "Unique Labor Identification Code"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_cuit
msgid "Unique Tax Identification Code"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__1
msgid "Untaxed"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Untaxed Amount"
msgstr ""

#. module: l10n_ar
#: model:product.template,name:l10n_ar.product_product_no_gravado_product_template
msgid "Untaxed concepts (VAT NT)"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_UpApP
msgid "UpApP"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_UpApP
msgid "Used by Anses for Padrón"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_vat
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_vat
msgid "VAT"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__l10n_ar_vat_afip_code
msgid "VAT AFIP Code"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_a_rg1415
msgid "VOUCHERS A OF SECTION A, INC. F), G.R. Nº 1415"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "VAT Content %s"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_exento
msgid "VAT Exempt"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_b_rg1415
msgid "VOUCHERS B OF ANNEX I, SECTION A, INC. F), G.R. Nº 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_c_rg1415
msgid "VOUCHERS C OF ANNEX I, SECTION A, INC. F), G.R. Nº 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_m_rg1415
msgid "VOUCHERS M OF ANNEX I, SECTION A, INC. F), G.R. NO. 1415"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cptag
msgid "WAYBILL FOR MOTOR TRANSPORT FOR GRAINS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,name:l10n_ar.dc_cptfg
msgid "WAYBILL FOR RAIL TRANSPORT FOR GRAINS"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid "What are my best achievement(s) since my last appraisal?"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"What are my short and long-term goals with the company, and for my\n"
"            career?"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid ""
"What has been the most challenging aspect of my work this past year and\n"
"            why?"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid "What would I need to improve my work?"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_exento
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_mono
#: model_terms:res.company,appraisal_employee_feedback_template:l10n_ar.company_ri
msgid "Which parts of my job do I most / least enjoy?"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "With Document"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"You are trying to create an invoice for domestic partner but you don't have "
"a domestic market journal"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"You are trying to create an invoice for foreign partner but you don't have "
"an exportation journal"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid ""
"You can not change %s journal's configuration if it already has validated "
"invoices"
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_exento
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_mono
#: model_terms:res.company,invoice_terms_html:l10n_ar.company_ri
msgid "You should update this document to reflect your T&amp;C."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,sign_terms:l10n_ar.company_exento
#: model_terms:res.company,sign_terms:l10n_ar.company_mono
#: model_terms:res.company,sign_terms:l10n_ar.company_ri
#: model_terms:res.company,sign_terms_html:l10n_ar.company_exento
#: model_terms:res.company,sign_terms_html:l10n_ar.company_mono
#: model_terms:res.company,sign_terms_html:l10n_ar.company_ri
msgid "Your conditions..."
msgstr ""

#. module: l10n_ar
#: model_terms:res.company,lunch_notify_message:l10n_ar.company_exento
#: model_terms:res.company,lunch_notify_message:l10n_ar.company_mono
#: model_terms:res.company,lunch_notify_message:l10n_ar.company_ri
msgid ""
"Your lunch has been delivered.\n"
"Enjoy your meal!"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.document.type,report_name:l10n_ar.dc_zeta
msgid "ZETA"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__l10n_latam_document_type__purchase_aliquots__zero
msgid "Zero"
msgstr ""

#. module: l10n_ar
#. odoo-python
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid "is not a valid value for"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,description:l10n_ar.it_ET
msgid "pending"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "to"
msgstr ""
