<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="gulf_cooperation_council_without_bh" model="res.country.group">
            <field name="name">Gulf Cooperation Council (GCC) without bh</field>
            <field name="country_ids" eval="[(6,0, [ref('base.sa'), ref('base.ae'), ref('base.om'), ref('base.qa'), ref('base.kw')])]"/>
        </record>
    </data>
</odoo>
