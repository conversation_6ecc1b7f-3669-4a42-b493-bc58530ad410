<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report_vat_return" model="account.report">
        <field name="name">1. VAT Return</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.eg"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_vat_return_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_vat_return_sale_base" model="account.report.line">
                <field name="name">VAT on Sales and all other Outputs (Base)</field>
                <field name="aggregation_formula">EG_STD_SALE_B.balance + EG_ZERO_SALE_B.balance + EG_EXM_SALE_B.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_vat_return_sale_base_fourteen" model="account.report.line">
                        <field name="name">1. Standard Rated 14% (Base)</field>
                        <field name="code">EG_STD_SALE_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_sale_base_fourteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">1. VAT 14% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_sale_base_zero" model="account.report.line">
                        <field name="name">2. Zero Rated (Base)</field>
                        <field name="code">EG_ZERO_SALE_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_sale_base_zero_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">2. Zero Rated (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_sale_base_exempt" model="account.report.line">
                        <field name="name">3. Exempt Sales (Base)</field>
                        <field name="code">EG_EXM_SALE_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_sale_base_exempt_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">3. Exempt Sales (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_vat_return_sale_tax" model="account.report.line">
                <field name="name">VAT on Sales and all other Outputs (Tax)</field>
                <field name="aggregation_formula">EG_STD_SALE_T.balance + EG_ZERO_SALE_T.balance + EG_EXM_SALE_T.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_vat_return_sale_tax_fourteen" model="account.report.line">
                        <field name="name">1. Standard Rated 14% (Tax)</field>
                        <field name="code">EG_STD_SALE_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_sale_tax_fourteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">1. VAT 14% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_sale_tax_zero" model="account.report.line">
                        <field name="name">2. Zero Rated (Tax)</field>
                        <field name="code">EG_ZERO_SALE_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_sale_tax_zero_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">2. Zero Rated (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_sale_tax_exempt" model="account.report.line">
                        <field name="name">3. Exempt Sales (Tax)</field>
                        <field name="code">EG_EXM_SALE_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_sale_tax_exempt_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">3. Exempt Sales (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_vat_return_expense_base" model="account.report.line">
                <field name="name">VAT on Expenses and all other Inputs (Base)</field>
                <field name="aggregation_formula">EG_STD_PUR_B.balance + EG_ZERO_PUR_B.balance + EG_EXM_PUR_B.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_vat_return_expense_base_fourteen" model="account.report.line">
                        <field name="name">5. Standard Rated 14% Expenses (Base)</field>
                        <field name="code">EG_STD_PUR_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_expense_base_fourteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">5. VAT 14% Expenses (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_expense_base_zero" model="account.report.line">
                        <field name="name">6. Zero Rated (Base)</field>
                        <field name="code">EG_ZERO_PUR_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_expense_base_zero_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">6. Zero Rated (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_expense_base_exempt" model="account.report.line">
                        <field name="name">7. Exempt Expenses (Base)</field>
                        <field name="code">EG_EXM_PUR_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_expense_base_exempt_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">7. Exempt Expenses (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_vat_return_expense_tax" model="account.report.line">
                <field name="name">VAT on Expenses and all other Inputs (Tax)</field>
                <field name="aggregation_formula">EG_STD_PUR_T.balance + EG_ZERO_PUR_T.balance + EG_EXM_PUR_T.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_vat_return_expense_tax_fourteen" model="account.report.line">
                        <field name="name">5. Standard Rated 14% Expenses (Tax)</field>
                        <field name="code">EG_STD_PUR_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_expense_tax_fourteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">5. VAT 14% Expenses (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_expense_tax_zero" model="account.report.line">
                        <field name="name">6. Zero Rated (Tax)</field>
                        <field name="code">EG_ZERO_PUR_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_expense_tax_zero_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">6. Zero Rated (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_vat_return_expense_tax_exempt" model="account.report.line">
                        <field name="name">7. Exempt Expenses (Tax)</field>
                        <field name="code">EG_EXM_PUR_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_vat_return_expense_tax_exempt_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">7. Exempt Expenses (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_vat_return_net" model="account.report.line">
                <field name="name">Net VAT Due</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_vat_return_net_1" model="account.report.line">
                        <field name="name">Total value of due tax for the period</field>
                        <field name="aggregation_formula">EG_STD_SALE_T.balance + EG_ZERO_SALE_T.balance + EG_EXM_SALE_T.balance</field>
                    </record>
                    <record id="tax_report_vat_return_net_2" model="account.report.line">
                        <field name="name">Total value of recoverable tax for the period</field>
                        <field name="aggregation_formula">EG_STD_PUR_T.balance + EG_ZERO_PUR_T.balance + EG_EXM_PUR_T.balance</field>
                    </record>
                    <record id="tax_report_vat_return_net_3" model="account.report.line">
                        <field name="name">Net VAT due (or reclaimed) for the period</field>
                        <field name="aggregation_formula">EG_STD_SALE_T.balance + EG_ZERO_SALE_T.balance + EG_EXM_SALE_T.balance - (EG_STD_PUR_T.balance + EG_ZERO_PUR_T.balance + EG_EXM_PUR_T.balance)</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
    <record id="tax_report_withholding_tax" model="account.report">
        <field name="name">2. Withholding Tax</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.eg"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_withholding_tax_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_withholding_tax_sale_base" model="account.report.line">
                <field name="name">Withholding Tax on Sales (Base)</field>
                <field name="aggregation_formula">EG_H_SALE_B.balance + EG_O_SALE_B.balance + EG_T_SALE_B.balance + EG_F_SALE_B.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_withholding_tax_sale_base_half" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -0.5% (Base)</field>
                        <field name="code">EG_H_SALE_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_base_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Sales -0.5% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_sale_base_one" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -1% (Base)</field>
                        <field name="code">EG_O_SALE_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_base_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH on Sales -1% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_sale_base_three" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -3% (Base)</field>
                        <field name="code">EG_T_SALE_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_base_three_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH on Sales -3% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_sale_base_five" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -5% (Base)</field>
                        <field name="code">EG_F_SALE_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_base_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH on Sales -5% (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_withholding_tax_sale_tax" model="account.report.line">
                <field name="name">Withholding Tax on Sales (Tax)</field>
                <field name="aggregation_formula">EG_H_SALE_T.balance + EG_O_SALE_T.balance + EG_T_SALE_T.balance + EG_F_SALE_T.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_withholding_tax_sale_tax_half" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -0.5% (Tax)</field>
                        <field name="code">EG_H_SALE_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_tax_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Sales -0.5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_sale_tax_one" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -1% (Tax)</field>
                        <field name="code">EG_O_SALE_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_tax_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Sales -1% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_sale_tax_three" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -3% (Tax)</field>
                        <field name="code">EG_T_SALE_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_tax_three_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Sales -3% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_sale_tax_five" model="account.report.line">
                        <field name="name">Withholding Tax on Sales -5% (Tax)</field>
                        <field name="code">EG_F_SALE_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_sale_tax_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Sales -5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_withholding_tax_purchase_base" model="account.report.line">
                <field name="name">Withholding Tax on Purchases (Base)</field>
                <field name="aggregation_formula">EG_H_PUR_B.balance + EG_O_PUR_B.balance + EG_T_PUR_B.balance + EG_F_PUR_B.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_withholding_tax_purchase_base_half" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -0.5% (Base)</field>
                        <field name="code">EG_H_PUR_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_base_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -0.5% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_purchase_base_one" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -1% (Base)</field>
                        <field name="code">EG_O_PUR_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_base_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -1% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_purchase_base_three" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -3% (Base)</field>
                        <field name="code">EG_T_PUR_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_base_three_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -3% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_purchase_base_five" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -5% (Base)</field>
                        <field name="code">EG_F_PUR_B</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_base_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -5% (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_withholding_tax_purchase_tax" model="account.report.line">
                <field name="name">Withholding Tax on Purchases (Tax)</field>
                <field name="aggregation_formula">EG_H_PUR_T.balance + EG_O_PUR_T.balance + EG_T_PUR_T.balance + EG_F_PUR_T.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_withholding_tax_purchase_tax_half" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -0.5% (Tax)</field>
                        <field name="code">EG_H_PUR_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_tax_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -0.5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_purchase_tax_one" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -1% (Tax)</field>
                        <field name="code">EG_O_PUR_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_tax_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -1% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_purchase_tax_three" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -3% (Tax)</field>
                        <field name="code">EG_T_PUR_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_tax_three_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -3% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_withholding_tax_purchase_tax_five" model="account.report.line">
                        <field name="name">Withholding Tax on Purchases -5% (Tax)</field>
                        <field name="code">EG_F_PUR_T</field>
                        <field name="expression_ids">
                            <record id="tax_report_withholding_tax_purchase_tax_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">WH Purchases -5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
    <record id="tax_report_schedule_tax" model="account.report">
        <field name="name">3. Schedule Tax</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.eg"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_schedule_tax_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_schedule_tax_schedule_tax_sale_base" model="account.report.line">
                <field name="name">Schedule Tax on Sales (Base)</field>
                <field name="aggregation_formula">EG_H_SALE_SB.balance + EG_O_SALE_SB.balance + EG_F_SALE_SB.balance + EG_E_SALE_SB.balance + EG_T_SALE_SB.balance + EG_FF_SALE_SB.balance + EG_TY_SALE_SB.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_schedule_tax_schedule_tax_sale_base_half" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 0.5% (Base)</field>
                        <field name="code">EG_H_SALE_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_base_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 0.5% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_base_one" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 1% (Base)</field>
                        <field name="code">EG_O_SALE_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_base_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 1% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_base_five" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 5% (Base)</field>
                        <field name="code">EG_F_SALE_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_base_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 5% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_base_eight" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 8% (Base)</field>
                        <field name="code">EG_E_SALE_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_base_eight_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 8% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_base_ten" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 10% (Base)</field>
                        <field name="code">EG_T_SALE_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_base_ten_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 10% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_base_fifteen" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 15% (Base)</field>
                        <field name="code">EG_FF_SALE_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_base_fifteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 15% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_base_thirty" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 30% (Base)</field>
                        <field name="code">EG_TY_SALE_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_base_thirty_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 30% (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_schedule_tax_schedule_tax_sale_tax" model="account.report.line">
                <field name="name">Schedule Tax on Sales (Tax)</field>
                <field name="aggregation_formula">EG_H_SALE_ST.balance + EG_O_SALE_ST.balance + EG_F_SALE_ST.balance + EG_E_SALE_ST.balance + EG_T_SALE_ST.balance + EG_FF_SALE_ST.balance + EG_TY_SALE_ST.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_schedule_tax_schedule_tax_sale_tax_half" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 0.5% (Tax)</field>
                        <field name="code">EG_H_SALE_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_tax_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 0.5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_tax_one" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 1% (Tax)</field>
                        <field name="code">EG_O_SALE_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_tax_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 1% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_tax_five" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 5% (Tax)</field>
                        <field name="code">EG_F_SALE_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_tax_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_tax_eight" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 8% (Tax)</field>
                        <field name="code">EG_E_SALE_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_tax_eight_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 8% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_tax_ten" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 10% (Tax)</field>
                        <field name="code">EG_T_SALE_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_tax_ten_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 10% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_tax_fifteen" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 15% (Tax)</field>
                        <field name="code">EG_FF_SALE_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_tax_fifteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 15% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_sale_tax_thirty" model="account.report.line">
                        <field name="name">Schedule Tax on Sales 30% (Tax)</field>
                        <field name="code">EG_TY_SALE_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_sale_tax_thirty_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Sales 30% (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_schedule_tax_schedule_tax_purchase_base" model="account.report.line">
                <field name="name">Schedule Tax on Purchases (Base)</field>
                <field name="aggregation_formula">EG_H_PUR_SB.balance + EG_O_PUR_SB.balance + EG_F_PUR_SB.balance + EG_E_PUR_SB.balance + EG_T_PUR_SB.balance + EG_FF_PUR_SB.balance + EG_TY_PUR_SB.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_base_half" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 0.5% (Base)</field>
                        <field name="code">EG_H_PUR_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_base_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 0.5% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_base_one" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 1% (Base)</field>
                        <field name="code">EG_O_PUR_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_base_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 1% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_base_five" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 5% (Base)</field>
                        <field name="code">EG_F_PUR_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_base_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 5% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_base_eight" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 8% (Base)</field>
                        <field name="code">EG_E_PUR_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_base_eight_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 8% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_base_ten" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 10% (Base)</field>
                        <field name="code">EG_T_PUR_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_base_ten_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 10% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_base_fifteen" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 15% (Base)</field>
                        <field name="code">EG_FF_PUR_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_base_fifteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 15% (Base)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_base_thirty" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 30% (Base)</field>
                        <field name="code">EG_TY_PUR_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_base_thirty_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 30% (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax" model="account.report.line">
                <field name="name">Schedule Tax on Purchases (Tax)</field>
                <field name="aggregation_formula">EG_H_PUR_ST.balance + EG_O_PUR_ST.balance + EG_F_PUR_ST.balance + EG_E_PUR_ST.balance + EG_T_PUR_ST.balance + EG_FF_PUR_ST.balance + EG_TY_PUR_ST.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_half" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 0.5% (Tax)</field>
                        <field name="code">EG_H_PUR_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_half_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 0.5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_one" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 1% (Tax)</field>
                        <field name="code">EG_O_PUR_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_one_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 1% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_five" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 5% (Tax)</field>
                        <field name="code">EG_F_PUR_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_five_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 5% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_eight" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 8% (Tax)</field>
                        <field name="code">EG_E_PUR_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_eight_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 8% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_ten" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 10% (Tax)</field>
                        <field name="code">EG_T_PUR_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_ten_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 10% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_fifteen" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 15% (Tax)</field>
                        <field name="code">EG_FF_PUR_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_fifteen_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 15% (Tax)</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_thirty" model="account.report.line">
                        <field name="name">Schedule Tax on Purchases 30% (Tax)</field>
                        <field name="code">EG_TY_PUR_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_schedule_tax_schedule_tax_purchase_tax_thirty_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">SCHD Purchases 30% (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
    <record id="tax_report_other_taxes" model="account.report">
        <field name="name">4. Other Taxes</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.eg"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_other_taxes_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_other_taxes_stamp_tax_base" model="account.report.line">
                <field name="name">Stamp Tax Sales (Base)</field>
                <field name="aggregation_formula">EG_STMP_TW_SB.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_other_taxes_stamp_tax_base_sales" model="account.report.line">
                        <field name="name">Stamp Tax Sales 20% (Base)</field>
                        <field name="code">EG_STMP_TW_SB</field>
                        <field name="expression_ids">
                            <record id="tax_report_other_taxes_stamp_tax_base_sales_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">Stamp Tax Sales 20% (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_other_taxes_stamp_tax_tax" model="account.report.line">
                <field name="name">Stamp Tax Sales (Tax)</field>
                <field name="aggregation_formula">EG_STMP_TW_ST.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_other_taxes_stamp_tax_tax_sales" model="account.report.line">
                        <field name="name">Stamp Tax Sales 20% (Tax)</field>
                        <field name="code">EG_STMP_TW_ST</field>
                        <field name="expression_ids">
                            <record id="tax_report_other_taxes_stamp_tax_tax_sales_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">Stamp Tax Sales 20% (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_other_taxes_stamp_purchase_tax_base" model="account.report.line">
                <field name="name">Stamp Tax Purchases (Base)</field>
                <field name="aggregation_formula">EG_STMP_TW_PB.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_other_taxes_stamp_purchase_tax_base_purchase" model="account.report.line">
                        <field name="name">Stamp Tax Purchases 20% (Base)</field>
                        <field name="code">EG_STMP_TW_PB</field>
                        <field name="expression_ids">
                            <record id="tax_report_other_taxes_stamp_purchase_tax_base_purchase_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">Stamp Tax Purchases 20% (Base)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_other_taxes_stamp_purchase_tax_tax" model="account.report.line">
                <field name="name">Stamp Tax Purchases (Tax)</field>
                <field name="aggregation_formula">EG_STMP_TW_PT.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_other_taxes_stamp_purchase_tax_tax_purchase" model="account.report.line">
                        <field name="name">Stamp Tax Purchases 20% (Tax)</field>
                        <field name="code">EG_STMP_TW_PT</field>
                        <field name="expression_ids">
                            <record id="tax_report_other_taxes_stamp_purchase_tax_tax_purchase_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">Stamp Tax Purchases 20% (Tax)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
