<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_annual_report_vat_vl" model="account.report">
        <field name="name">VL VAT Report</field>
        <field name="sequence">1</field>
        <field name="country_id" ref="base.it"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_annual_report_vat_balance_vl" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_annual_report_line_comp_fram" model="account.report.line">
                <field name="name">Annual tax for completed schedules</field>
                <field name="code">VL</field>
                <field name="children_ids">
                    <record id="tax_annual_report_line_comp_fram_1" model="account.report.line">
                        <field name="name">Determination of VAT due or credit for the tax period</field>
                        <field name="code">VL_1</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_vl1" model="account.report.line">
                                <field name="name">VL1 - VAT payable</field>
                                <field name="code">VL1</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_vl1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE26.balance + VJ19.balance</field>
                                        <field name="subformula">cross_report</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_vl2" model="account.report.line">
                                <field name="name">VL2 - VAT deductible</field>
                                <field name="code">VL2</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_vl2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF71.tax</field>
                                        <field name="subformula">cross_report</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_vl3" model="account.report.line">
                                <field name="name">VL3 - Tax Due</field>
                                <field name="code">VL3</field>
                                <field name="aggregation_formula">VL1.balance - VL2.balance</field>
                            </record>
                            <record id="tax_annual_report_line_vl4" model="account.report.line">
                                <field name="name">VL4 - Tax Credit</field>
                                <field name="code">VL4</field>
                                <field name="aggregation_formula">VL2.balance - VL1.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_comp_fram_2" model="account.report.line">
                        <field name="name">Previous year credit</field>
                        <field name="code">VL_2</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_vl8" model="account.report.line">
                                <field name="name">VL8 - Credit resulting from the previous year's declaration</field>
                                <field name="code">VL8</field>
                                <field name="tax_tags_formula">vl8</field>
                            </record>
                            <record id="tax_annual_report_line_vl9" model="account.report.line">
                                <field name="name">VL9 - Credit offset in the template</field>
                                <field name="code">VL9</field>
                                <field name="tax_tags_formula">vl9</field>
                            </record>
                            <record id="tax_annual_report_line_vl10" model="account.report.line">
                                <field name="name">VL10 - Non-transferable credit surplus</field>
                                <field name="code">VL10</field>
                                <field name="tax_tags_formula">vl10</field>
                            </record>
                            <record id="tax_annual_report_line_vl11" model="account.report.line">
                                <field name="name">VL11 - Credits art. 8, paragraph 6-quater, Presidential Decree. n. 322/98</field>
                                <field name="code">VL11</field>
                                <field name="tax_tags_formula">vl11</field>
                            </record>
                            <record id="tax_annual_report_line_vl12" model="account.report.line">
                                <field name="name">VL12 - Periodic payments omitted</field>
                                <field name="code">VL12</field>
                                <field name="tax_tags_formula">vl12</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_comp_fram_3" model="account.report.line">
                        <field name="name">Determination of debit or credit VAT relating to all activities carried out</field>
                        <field name="code">VL_3</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_vl20" model="account.report.line">
                                <field name="name">VL20 - Interim refunds required</field>
                                <field name="code">VL20</field>
                                <field name="tax_tags_formula">vl20</field>
                            </record>
                            <record id="tax_annual_report_line_vl21" model="account.report.line">
                                <field name="name">VL21 - Amount of credits transferred</field>
                                <field name="code">VL21</field>
                                <field name="tax_tags_formula">vl21</field>
                            </record>
                            <record id="tax_annual_report_line_vl22" model="account.report.line">
                                <field name="name">VL22 - VAT credit resulting from the first 3 quarters</field>
                                <field name="code">VL22</field>
                                <field name="tax_tags_formula">vl22</field>
                            </record>
                            <record id="tax_annual_report_line_vl23" model="account.report.line">
                                <field name="name">VL23 - Interest due for quarterly payments</field>
                                <field name="code">VL23</field>
                                <field name="tax_tags_formula">vl23</field>
                            </record>
                            <record id="tax_annual_report_line_vl24" model="account.report.line">
                                <field name="name">VL24 - Previous year transfers returned by the parent company</field>
                                <field name="code">VL24</field>
                                <field name="tax_tags_formula">vl24</field>
                            </record>
                            <record id="tax_annual_report_line_vl25" model="account.report.line">
                                <field name="name">VL25 - Previous year credit surplus</field>
                                <field name="code">VL25</field>
                                <field name="aggregation_formula">VL8.balance - VL9.balance</field>
                            </record>
                            <record id="tax_annual_report_line_vl26" model="account.report.line">
                                <field name="name">VL26 - Credit requested for reimbursement in previous years computable as a deduction following refusal by the office</field>
                                <field name="code">VL26</field>
                                <field name="tax_tags_formula">vl26</field>
                            </record>
                            <record id="tax_annual_report_line_vl27" model="account.report.line">
                                <field name="name">VL27 - Tax credits used in periodic payments and for the advance payment</field>
                                <field name="code">VL27</field>
                                <field name="tax_tags_formula">vl27</field>
                            </record>
                            <record id="tax_annual_report_line_vl28" model="account.report.line">
                                <field name="name">VL28 - Credits received from savings management companies used in periodic payments and for the advance payment</field>
                                <field name="code">VL28</field>
                                <field name="tax_tags_formula">vl28</field>
                            </record>
                            <record id="tax_annual_report_line_vl29" model="account.report.line">
                                <field name="name">VL29 - EU car payments relating to sales carried out during the year</field>
                                <field name="code">VL29</field>
                                <field name="tax_tags_formula">vl29</field>
                            </record>
                            <record id="tax_annual_report_line_vl30" model="account.report.line">
                                <field name="name">VL30 - Periodic VAT amount</field>
                                <field name="code">VL30</field>
                                <field name="children_ids">
                                    <record id="tax_annual_report_line_vl30_I" model="account.report.line">
                                        <field name="name">Periodic VAT due</field>
                                        <field name="code">VL30_I</field>
                                        <field name="tax_tags_formula">vl30_i</field>
                                    </record>
                                    <record id="tax_annual_report_line_vl30_II" model="account.report.line">
                                        <field name="name">Periodic VAT paid</field>
                                        <field name="code">VL30_II</field>
                                        <field name="tax_tags_formula">vl30_ii</field>
                                    </record>
                                    <record id="tax_annual_report_line_vl30_III" model="account.report.line">
                                        <field name="name">Periodic VAT paid following notification of irregularities</field>
                                        <field name="code">VL30_III</field>
                                        <field name="tax_tags_formula">vl30_iii</field>
                                    </record>
                                    <record id="tax_annual_report_line_vl30_IV" model="account.report.line">
                                        <field name="name">Periodic VAT paid following payment orders</field>
                                        <field name="code">VL30_IV</field>
                                        <field name="tax_tags_formula">vl30_iv</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_vl31" model="account.report.line">
                                <field name="name">VL31 - Amount of debts transferred</field>
                                <field name="code">VL31</field>
                                <field name="tax_tags_formula">vl31</field>
                            </record>
                            <record id="tax_annual_report_line_vl32" model="account.report.line">
                                <field name="name">VL32 - VAT DUE</field>
                                <field name="code">VL32</field>
                                <field name="aggregation_formula">(VL3.balance + VL20.balance + VL21.balance + VL22.balance + VL23.balance)
                                                    - (VL4.balance + VL11.balance + VL24.balance + VL25.balance + VL26.balance +
                                                        VL27.balance + VL28.balance + VL29.balance + VL30_I.balance + VL30_II.balance +
                                                        VL30_III.balance + VL30_IV.balance + VL31.balance)</field>
                            </record>
                            <record id="tax_annual_report_line_vl33" model="account.report.line">
                                <field name="name">VL33 - VAT CREDIT</field>
                                <field name="code">VL33</field>
                                <field name="aggregation_formula">(VL4.balance + VL11.balance + VL24.balance + VL25.balance + VL26.balance +
                                                        VL27.balance + VL28.balance + VL29.balance + VL30_I.balance + VL30_II.balance +
                                                        VL30_III.balance + VL30_IV.balance + VL31.balance) - (VL3.balance + VL20.balance +
                                                        VL21.balance + VL22.balance + VL23.balance)</field>
                            </record>
                            <record id="tax_annual_report_line_vl34" model="account.report.line">
                                <field name="name">VL34 - Tax credits used in the annual return</field>
                                <field name="code">VL34</field>
                                <field name="tax_tags_formula">vl34</field>
                            </record>
                            <record id="tax_annual_report_line_vl35" model="account.report.line">
                                <field name="name">VL35 - Credits received from asset management companies used in the annual declaration</field>
                                <field name="code">VL35</field>
                                <field name="tax_tags_formula">vl35</field>
                            </record>
                            <record id="tax_annual_report_line_vl36" model="account.report.line">
                                <field name="name">VL36 - Interest due on the annual return</field>
                                <field name="code">VL36</field>
                                <field name="tax_tags_formula">vl36</field>
                            </record>
                            <record id="tax_annual_report_line_vl37" model="account.report.line">
                                <field name="name">VL37 - Credit transferred by savings management companies pursuant to art. 8 of the legislative decree n. 351/2001</field>
                                <field name="code">VL37</field>
                                <field name="tax_tags_formula">vl37</field>
                            </record>
                            <record id="tax_annual_report_line_vl38" model="account.report.line">
                                <field name="name">VL38 - TOTAL VAT DUE</field>
                                <field name="code">VL38</field>
                                <field name="aggregation_formula">VL32.balance - VL34.balance - VL35.balance + VL36.balance</field>
                            </record>
                            <record id="tax_annual_report_line_vl39" model="account.report.line">
                                <field name="name">VL39 - TOTAL VAT INPUT</field>
                                <field name="code">VL39</field>
                                <field name="aggregation_formula">VL33.balance - VL37.balance</field>
                            </record>
                            <record id="tax_annual_report_line_vl40" model="account.report.line">
                                <field name="name">VL40 - Payments made following excess use of credit</field>
                                <field name="code">VL40</field>
                                <field name="tax_tags_formula">vl40</field>
                            </record>
                            <record id="tax_annual_report_line_vl41_I" model="account.report.line">
                                <field name="name">VL41 - Difference between periodic VAT due and periodic VAT paid</field>
                                <field name="code">VL41_I</field>
                                <field name="aggregation_formula">VL30_I.balance - (VL30_II.balance + VL30_III.balance + VL30_IV.balance)</field>
                            </record>
                            <record id="tax_annual_report_line_vl41_II" model="account.report.line">
                                <field name="name">VL41 - Difference between credit potential and actual credit</field>
                                <field name="code">VL41_II</field>
                                <field name="aggregation_formula">(VL4.balance + VL11.balance + VL12.balance + VL24.balance +
                                                        VL25.balance + VL26.balance + VL27.balance + VL28.balance +
                                                        VL29.balance + VL30_I.balance + VL31.balance) - (VL3.balance + VL20.balance +
                                                        VL21.balance + VL22.balance + VL23.balance)</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
