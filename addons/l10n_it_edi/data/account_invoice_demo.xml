<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- add VAT, codice fiscal and tax system for main company -->
        <record id="l10n_it.demo_company_it" model="res.company">
            <field name="vat">IT***********</field>
            <field name="street">Test Street</field>
            <field name="city">Prova</field>
            <field name="zip">12345</field>
            <field name="l10n_it_codice_fiscale">***********</field>
            <field name="l10n_it_tax_system">RF01</field>
            <field name="zip">12345</field>
        </record>

        <record id="l10n_it.partner_demo_company_it" model="res.partner">
            <field name="l10n_it_pa_index">0803HR0</field>
        </record>

        <record id="partner_demo_it" model="res.partner">
            <field name="name">Palazzo dell'Arte</field>
            <field name="vat">*************</field>
            <field name="street">Piazza Marconi 5</field>
            <field name="city">Cremona</field>
            <field name="country_id" ref="base.it"/>
            <field name="state_id" ref="base.state_it_cr"/>
            <field name="zip">26000</field>
            <field name="email"><EMAIL></field>
            <field name="website">www.itexample.com</field>
        </record>

        <record id="demo_l10n_it_edi_bank" model="res.partner.bank">
            <field name="acc_type">iban</field>
            <field name="acc_number">****************</field>
            <field name="bank_id" ref="base.bank_bnp"/>
            <field name="partner_id" ref="l10n_it.partner_demo_company_it"/>
            <field name="company_id" ref="l10n_it.demo_company_it"/>
        </record>

        <record id="demo_l10n_it_edi_partner_a" model="res.partner">
            <field name="name">Biscotti Oslenghi</field>
            <field name="company_type">company</field>
            <field name="country_id" ref="base.it"/>
            <field name="street">1234 Strada del Caffè</field>
            <field name="city">Milano</field>
            <field name="zip">20100</field>
            <field name="vat">IT***********</field>
            <field name="l10n_it_codice_fiscale">***********</field>
            <field name="l10n_it_pa_index">N8MIMM9</field>
        </record>

        <record id="demo_l10n_it_edi_partner_pa" model="res.partner">
            <field name="name">Agenzia Regionale Emergenza Urgenza</field>
            <field name="company_type">company</field>
            <field name="country_id" ref="base.it"/>
            <field name="street">Via Alfredo Campanini 6</field>
            <field name="city">Milano</field>
            <field name="zip">20124</field>
            <field name="vat">IT***********</field>
            <field name="l10n_it_codice_fiscale">***********</field>
            <field name="l10n_it_pa_index">SOOTJS</field>
        </record>

        <record id="demo_l10n_it_edi_proxy_user" model="account_edi_proxy_client.user">
            <field name="id_client">demo_id_client</field>
            <field name="company_id" ref="l10n_it.demo_company_it"/>
            <field name="proxy_type">l10n_it_edi</field>
            <field name="edi_mode">demo</field>
            <field name="edi_identification">***********</field>
            <field name="private_key">1234</field>
            <field name="refresh_token">demo</field>
        </record>

    </data>
</odoo>
