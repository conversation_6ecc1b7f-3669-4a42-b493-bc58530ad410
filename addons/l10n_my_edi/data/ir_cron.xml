<?xml version="1.0" ?>
<odoo>
    <data>
        <record id="ir_cron_myinvois_sync" model="ir.cron">
            <field name="name">MyInvois: Synchronization</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="state">code</field>
            <field name="code">model._cron_l10n_my_edi_synchronize_myinvois()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(hours=1))"/>
        </record>
    </data>
</odoo>
