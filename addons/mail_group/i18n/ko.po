# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_group
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid ") <span invisible=\"not send_email\">and send them an email</span>."
msgstr ") <span invisible=\"not send_email\">을 클릭하고 이메일을 전송합니다</span>."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "<br/> You'll be notified as soon as some new content is posted."
msgstr "<br/> 새로운 콘텐츠가 게시되는 즉시 알림을 받게됩니다."

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_subscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br><br>\n"
"                You have requested to be subscribed to the mailing list <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>\n"
"                <br><br>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                안녕하세요,<br><br>\n"
"                메일링 리스트 구독을 신청해 주셔서 감사합니다. <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                확인하려면 다음 링크를 눌러주세요: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>\n"
"                <br><br>\n"
"                구독을 요청하지 않으셨거나 실수로 누르신 경우 이 메시지는 무시해 주십시오.\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_unsubscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br><br>\n"
"                You have requested to be unsubscribed to the mailing list <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>.\n"
"                <br><br>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                안녕하세요,<br><br>\n"
"                메일링 리스트 구독 해지를 신청하셨습니다.<strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                확인하려면 다음 링크를 눌러주세요: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>.\n"
"                <br><br>\n"
"                구독 취소를 요청하지 않으셨거나 실수로 누르신 경우 이 메시지는 무시해 주십시오.\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_guidelines
msgid ""
"<div>\n"
"                <p>Hello <t t-out=\"object.partner_id.name or ''\"></t>,</p>\n"
"                <p>Please find below the guidelines of the <t t-out=\"object.mail_group_id.name\"></t> mailing list.</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"></t></p>\n"
"            </div>\n"
"        "
msgstr ""
"<div>\n"
"                <p>안녕하세요, <t t-out=\"object.partner_id.name or ''\"></t>님.</p>\n"
"                <p>하기 <t t-out=\"object.mail_group_id.name\"></t>메일링 리스트</p>의 가이드라인을 참조해 주시기 바랍니다.\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"></t></p>\n"
"            </div>\n"
"        "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Alias\" "
"title=\"Alias\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_name
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"Previous message\" "
"title=\"Previous message\"/>"
msgstr "<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"이전 메시지\" title=\"이전 메시지\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"Next message\" "
"title=\"Next message\"/>"
msgstr "<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"다음 메시지\" title=\"다음 메시지\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show attachments\""
" title=\"Show attachments\"/>"
msgstr ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"첨부 파일 보기\" "
"title=\"첨부 파일 보기\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show replies\" "
"title=\"Show replies\"/>"
msgstr "<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"답장 보기\" title=\"답장 보기\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide "
"attachments\" title=\"Hide attachments\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"첨부 파일 숨기기\" "
"title=\"첨부 파일 숨기기\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide replies\" "
"title=\"Hide replies\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"답장 숨기기\" "
"title=\"답장 숨기기\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "<span class=\"bg-warning\">Pending</span>"
msgstr "<span class=\"bg-warning\">대기 중</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">Whitelisted</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">Banned</span>"
msgstr ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">화이트리스트에 추가됨</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">금지됨</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<span class=\"mx-2\">-</span>"
msgstr "<span class=\"mx-2\">-</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "<span>By thread</span>"
msgstr "<span>스레드별별</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<span>No Mail Group yet.</span>\n"
"                    <br/>"
msgstr ""
"<span>아직 메일 그룹이 없습니다.</span>\n"
"                    <br/>"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "이 별칭에 대한 새 레코드를 만들 때 기본값을 제공하도록 평가되는 Python 사전입니다."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Accept"
msgstr "수락"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__accepted
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Accepted"
msgstr "수락됨"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__action
msgid "Action"
msgstr "추가 작업"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__active
msgid "Active"
msgstr "활성화"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Add this email address to white list of people and accept all pending "
"messages from the same author."
msgstr "이 이메일 주소를 화이트리스트에 추가하고 동일한 작성자가 보낸 모든 대기 중 메시지를 수락하세요."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Alias"
msgstr "별칭"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_contact
msgid "Alias Contact Security"
msgstr "별칭 연락처 보안"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain_id
msgid "Alias Domain"
msgstr "별칭 도메인"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain
msgid "Alias Domain Name"
msgstr "별칭 도메인 이름"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_full_name
msgid "Alias Email"
msgstr "이메일 별칭"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_name
msgid "Alias Name"
msgstr "별칭 이름"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_status
msgid "Alias Status"
msgstr "별칭 상태"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_status
msgid "Alias status assessed on the last message received."
msgstr "최근 받은메일에서 확인한 별칭 상태입니다."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_model_id
msgid "Aliased Model"
msgstr "별칭 모델"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "All messages of this group"
msgstr "이 그룹의 모든 메시지"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Allowed Emails"
msgstr "허용된 이메일"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Alone we can do so little, together we can do so much"
msgstr "혼자서는 조금할 수 있지만 함께 한다면 더 많이 할 수 있습니다"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__allow
msgid "Always Allow"
msgstr "항상 허용"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "An email with instructions has been sent."
msgstr "지침이 담긴 이메일이 전송되었습니다."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Archived"
msgstr "보관됨"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "Archives"
msgstr "보관"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__attachment_ids
msgid "Attachments"
msgstr "첨부 파일"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_id
msgid "Author"
msgstr "작성자"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_moderation
msgid "Author Moderation Status"
msgstr "작성자 검토 상태"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"메시지 작성자입니다. 설정하지 않으면 이메일의 보낸 사람 항목에 해당 협력사가 아닌 다른 이메일 주소가 선택될 수 있습니다. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_group_id
msgid "Authorized Group"
msgstr "인증 그룹"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify
msgid "Automatic notification"
msgstr "자동 알림"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__ban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Ban"
msgstr "금지"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Ban the author of the message ("
msgstr "메시지 작성자 차단 ("

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Ban this email address and reject all pending messages from the same author "
"and send an email to the author"
msgstr "이 이메일 주소를 차단하고 동일한 작성자가 보낸 모든 대기 중 메시지를 거부한 후, 작성자에게 이메일을 전송합니다."

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__ban
msgid "Banned"
msgstr "차단됨"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Banned Emails"
msgstr "금지된 이메일"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "By date"
msgstr "날짜별"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__can_manage_group
msgid "Can Manage"
msgstr "관리 가능"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__can_manage_group
msgid "Can manage the members"
msgstr "멤버 관리 가능"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_child_ids
msgid "Children"
msgstr "하위"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Choose or configure a custom domain"
msgstr "사용자 지정 도메인 선택 또는 환경설정"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_subscribe
msgid "Confirm subscription to {{ object.name }}"
msgstr "{{ object.name }}에 대한 구독을 확인"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_unsubscribe
msgid "Confirm unsubscription to {{ object.name }}"
msgstr "{{ object.name }} 구독 취소 확인"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__body
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__body
msgid "Contents"
msgstr "콘텐츠"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid "Create a Mail Group"
msgstr "메일 그룹 생성"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Create a new group"
msgstr "새 그룹 생성"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_uid
msgid "Created by"
msgstr "작성자"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_date
msgid "Created on"
msgstr "작성일자"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__is_moderator
msgid "Current user is a moderator of the group"
msgstr "현재 사용자가 그룹의 중재자입니다."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "고객 반송 메시지"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_defaults
msgid "Default Values"
msgstr "기본값"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__description
msgid "Description"
msgstr "설명"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Discard"
msgstr "취소"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__display_name
msgid "Display Name"
msgstr "표시명"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__email
msgid "Email"
msgstr "이메일"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Email %s is invalid"
msgstr "이메일%s이 유효하지 않습니다 "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_email
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Email Alias"
msgstr "이메일 별칭"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__email_from_normalized
msgid "Email From"
msgstr "이메일 보낸 사람"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr "보낸 사람의 이메일 주소입니다. 이 필드는 일치하는 협력사가 없는 경우에 설정되어 메시지 창에서 작성자 필드를 대체합니다."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "이메일 도메인 예: '<EMAIL>'의 'example.com'"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails"
msgstr "이메일"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails waiting an action for this group"
msgstr "이 그룹에 대한 조치를 기다리는 이메일"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__public
msgid "Everyone"
msgstr "전체"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Follow-Ups"
msgstr "후속 조치"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "From"
msgstr "발신인"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__mail_group_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Group"
msgstr "그룹"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Group By"
msgstr "그룹별"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Group Message"
msgstr "그룹 메시지"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Group Name"
msgstr "그룹명"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Group message can only be linked to mail group. Current model is %s."
msgstr "그룹 메시지는 메일 그룹에만 연결이 가능합니다. 현재 모델은 %s입니다."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines_msg
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Guidelines"
msgstr "지침"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_guidelines
msgid "Guidelines of group {{ object.mail_group_id.name }}"
msgstr "{{ object.mail_group_id.name }} 그룹의 가이드라인"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Hello"
msgstr "안녕하세요"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__id
msgid "ID"
msgstr "ID"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "별칭을 담고 있는 상위 레코드의 ID (예: 별칭을 생성한 작업을 담고 있는 프로젝트)"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "설정할 경우, 권한이 없는 사용자에게 기본 메시지 대신 이 내용을 전송합니다. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__image_128
msgid "Image"
msgstr "이미지"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Invalid action for URL generation (%s)"
msgstr "URL 생성에 대한 잘못된 작업 (%s)"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_moderation.py:0
#: code:addons/mail_group/models/mail_group_moderation.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "잘못된 이메일 주소 %r"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.invalid_token_subscription
msgid "Invalid or expired confirmation link."
msgstr "확인 링크가 잘못되었거나 만료되었습니다."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Allowed"
msgstr "허용 여부"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Banned"
msgstr "금지 대상 여부"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__is_group_moderated
msgid "Is Group Moderated"
msgstr "그룹이 조정됨"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_member
msgid "Is Member"
msgstr "회원 여부"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Join"
msgstr "참여"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Leave"
msgstr "나가기"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "Let people subscribe to your list online or manually add them here."
msgstr "온라인으로 목록을 구독하거나 여기에서 직접 추가할 수 있도록 합니다."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "로컬 부품 기반 입고 감지"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Mail Group"
msgstr "메일 그룹"

#. module: mail_group
#: model:res.groups,name:mail_group.group_mail_group_manager
msgid "Mail Group Administrator"
msgstr "메일 그룹 관리자"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_subscribe
msgid "Mail Group: Mailing List Subscription"
msgstr "메일 그룹: 메일링 리스트 구독"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_unsubscribe
msgid "Mail Group: Mailing List Unsubscription"
msgstr "메일 그룹: 메일링 리스트 구독 취소"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_guidelines
msgid "Mail Group: Send Guidelines"
msgstr "메일 그룹: 가이드라인 전송"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_action
#: model:ir.ui.menu,name:mail_group.mail_group_menu
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Mail Groups"
msgstr "메일 그룹"

#. module: mail_group
#: model:ir.actions.server,name:mail_group.ir_cron_mail_notify_group_moderators_ir_actions_server
msgid "Mail List: Notify group moderators"
msgstr "메일 리스트: 그룹 관리자 알림"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_message_id
msgid "Mail Message"
msgstr "메일 메시지"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_member
msgid "Mailing List Member"
msgstr "메일링 리스트 회원"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message
msgid "Mailing List Message"
msgstr "메일링 리스트 메시지"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_moderation
msgid "Mailing List black/white list"
msgstr "메일링 리스트 블랙/화이트리스트"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.portal_breadcrumbs_group
msgid "Mailing Lists"
msgstr "메일보내기 목록"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid ""
"Mailing groups are communities that like to discuss a specific topic "
"together."
msgstr "메일 그룹은 특정 주제에 대해 함께 토론하는 것을 좋아하는 커뮤니티입니다."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Mailing-List:"
msgstr "메일링 리스트:"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Member"
msgstr "회원"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_member_action
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Members"
msgstr "회원"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_count
msgid "Members Count"
msgstr "멤버 수"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Members of this group"
msgstr "이 그룹의 멤버"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__members
msgid "Members only"
msgstr "회원 전용"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__mail_group_message_id
msgid "Message"
msgstr "메시지"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_reject_action
msgid "Message Rejection Explanation"
msgstr "메시지 수신 거부 설명"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_action
msgid "Messages"
msgstr "메시지"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_count
msgid "Messages Count"
msgstr "메시지 수"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_last_month_count
msgid "Messages Per Month"
msgstr "월별 메시지"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Messages are pending moderation"
msgstr "메시지가 검토 대기 중입니다."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Messages that need an action"
msgstr "조치가 필요한 메시지"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Moderate Messages"
msgstr "메시지 중재하기"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation
msgid "Moderate this group"
msgstr "이 그룹 중재하기"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderated"
msgstr "중재됨"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderator_id
msgid "Moderated By"
msgstr "중재자"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_ids
msgid "Moderated Emails"
msgstr "중재한 이메일"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_count
msgid "Moderated emails count"
msgstr "중재한 이메일 숫자"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderated emails in this group"
msgstr "이 그룹에서 검토된 이메일"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Moderated group must have moderators."
msgstr "중재 그룹에는 중재자가 있어야 합니다."

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_moderation_action
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderation"
msgstr "중재중"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_tree
msgid "Moderation Lists"
msgstr "중재중인 목록"

#. module: mail_group
#: model:ir.ui.menu,name:mail_group.mail_group_moderation_menu
msgid "Moderation Rules"
msgstr "중재 규칙"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderations"
msgstr "중재"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_moderator
msgid "Moderator"
msgstr "중재자"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderator_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderators"
msgstr "중재자"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Moderators must have an email address."
msgstr "중재자는 이메일 주소가 있어야 합니다."

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_1
msgid "My Company News"
msgstr "내 회사 뉴스"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__name
msgid "Name"
msgstr "이름"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"Need to unsubscribe? <br/>It's right here! <span class=\"oi fa-2x oi-arrow-"
"down float-end\" role=\"img\" aria-label=\"\" title=\"Read this !\"/>"
msgstr ""
"구독 취소하시겠습니까? <br/>여기를 눌러주세요. <span class=\"oi fa-2x oi-arrow-down float-"
"end\" role=\"img\" aria-label=\"\" title=\"Read this !\"/>"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_guidelines
msgid ""
"Newcomers on this moderated group will automatically receive the guidelines."
msgstr "이 중재 그룹의 신규 사용자는 자동으로 가이드라인을 받게 됩니다."

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "No Members in this list yet!"
msgstr "이 리스트에 아직 회원이 없습니다."

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid "No Messages in this list yet!"
msgstr "이 리스트에 아직 메시지가 없습니다."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email_normalized
msgid "Normalized Email"
msgstr "정규화된 이메일"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from_normalized
msgid "Normalized From"
msgstr "정규화"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify_msg
msgid "Notification message"
msgstr "알림 메시지"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Notify Members"
msgstr "회원 알림"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_count
msgid "Number of message in this group"
msgstr "그룹 내 메시지 수"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid ""
"Only an administrator or a moderator can send guidelines to group members."
msgstr "관리자나 중재자만 그룹 회원에게 가이드라인을 전송할 수 있습니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Only members can send email to the mailing list."
msgstr "회원만 메일링 리스트에 이메일을 전송할 수 있습니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Only selected groups of users can send email to the mailing list."
msgstr "선택한 사용자 그룹만 메일링 리스트에 이메일을 보낼 수 있습니다."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"회신하지 않은 것까지 모든 받는 메시지를 첨부할 스레드(레코드)의 선택사항 ID. 설정하면 완벽하게 새로운 레코드의 생성을 억제합니다."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_parent_id
msgid "Parent"
msgstr "상위"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_model_id
msgid "Parent Model"
msgstr "상위 모델"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "상위 레코드 스레드 ID"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"별칭이 있는 상위 모델입니다. 별칭 참조를 포함하는 모델은 반드시 alias_model_id(예: 프로젝트(parent_model) 및 "
"태스크(model)에서 제공하는 모델일 필요는 없습니다.)"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__partner_id
msgid "Partner"
msgstr "협력사"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_partner_ids
msgid "Partners Member"
msgstr "협력사 회원"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_ids
msgid "Pending Messages"
msgstr "보류 메시지"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Pending Messages Count"
msgstr "보류 메시지 수"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__pending_moderation
msgid "Pending Moderation"
msgstr "중재를 위해 대기 중"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr "사람들은 중재를 기다리는 메시지에 대한 자동 알림을 받습니다."

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__ban
msgid "Permanent Ban"
msgstr "영구 금지"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mailgateway를 사용하여 문서에 메시지를 게시하는 정책.\n"
"- 모든 사용자: 모든 사용자가 게시할 수 있습니다\n"
"- 협력사: 인증된 협력사만 게시할 수 있습니다\n"
"- 팔로워: 관련 문서의 팔로워 또는 팔로잉 중인 채널의 사용자만 게시할 수 있습니다\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Post to:"
msgstr "게시 대상:"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_date
msgid "Posted"
msgstr "발행 완료"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_mode
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Privacy"
msgstr "비공개"

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_2
msgid "Public Mailing List"
msgstr "공개 메일링 리스트"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/wizard/mail_group_message_reject.py:0
#, python-format
msgid "Re: %s"
msgstr "회신 : %s"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_force_thread_id
msgid "Record Thread ID"
msgstr "레코드 스레드 ID"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Reference"
msgstr "참조"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__reject
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Reject"
msgstr "거부"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message_reject
msgid "Reject Group Message"
msgstr "그룹 메시지 수신 거부"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject Silently"
msgstr "알림 거부"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject the message"
msgstr "메시지 수신 거부"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__rejected
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Rejected"
msgstr "거부됨"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Remove message with explanation"
msgstr "설명이 있는 메시지 제거"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Responsible Users"
msgstr "담당 사용자"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "Search Group Message"
msgstr "그룹 메시지 검색"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_member_view_search
msgid "Search Mail Group Member"
msgstr "메일 그룹 회원 검색"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Search Mail group"
msgstr "메일 그룹 검색"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Search Moderation List"
msgstr "중재중인 목록 검색"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__groups
msgid "Selected group of users"
msgstr "선택한 사용자 그룹"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Send"
msgstr "보내기"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Ban"
msgstr "전송 및 차단"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Reject"
msgstr "전송 및 거부"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__send_email
msgid "Send Email"
msgstr "이메일 보내기"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Send Guidelines"
msgstr "가이드라인 전송"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message_reject__send_email
msgid "Send an email to the author of the message"
msgstr "메시지 작성자에게 이메일 보내기"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr "신규 구독자에게 지침 보내기"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_guidelines
msgid "Sent to people who subscribed to a mailing group with group guidelines"
msgstr "가이드라인이 포함된 메일링 그룹 회원에게 전송됩니다"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_unsubscribe
msgid "Sent to people who unsubscribed from a mailing group"
msgstr "메일링 그룹 구독을 취소한 사람에게 전송됩니다"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderation_status
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__status
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Status"
msgstr "상태"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Stay in touch with our Community"
msgstr "커뮤니티와 연락을 유지하십시오"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__subject
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__subject
msgid "Subject"
msgstr "제목"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
#, python-format
msgid "Subscribe"
msgstr "구독"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_subscribe
msgid "Subscription confirmation to a mailing group"
msgstr "메일링 그룹에 대한 구독 확인"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid ""
"Template \"mail_group.mail_template_guidelines\" was not found. No email has"
" been sent. Please contact an administrator to fix this issue."
msgstr ""
"\"mail_group.mail_template_guidelines\" 서식을 찾을 수 없습니다. 이메일이 전송되지 않았습니다. 이 "
"문제를 해결하려면 관리자에게 문의하십시오."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Thank you!"
msgstr "감사합니다!"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The \"Authorized Group\" is missing."
msgstr "\"인증된 그룹\"이 없습니다."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "The email"
msgstr "이메일"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "The email \"%s\" is not valid."
msgstr "이메일\"%s\" 이 올바르지 않습니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The group of the message do not match."
msgstr "메시지 그룹이 일치하지 않습니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The guidelines description is empty."
msgstr "가이드라인 설명이 비어 있습니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The guidelines description is missing."
msgstr "가이드라인 설명이 없습니다."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"이 별칭에 해당하는 모델(Odoo 문서 종류)입니다. 모든 받는 메일은 기존 레코드로 기록되지 않고 이 모델의 새 레코드로 작성됩니다. "
"(예. 프로젝트 작업)"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"<<EMAIL>>에 대한 이메일을 수신하도록 하려면 이메일의 별칭 이름을 적어주세요. 예: 'jobs'"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The notification message is missing."
msgstr "알림 메시지가 없습니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The partner can not be found."
msgstr "협력사를 찾을 수 없습니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "The record of the message should be the group."
msgstr "메시지의 레코드는 그룹이어야 합니다."

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "This email is already subscribed."
msgstr "이미 구독 중인 이메일입니다."

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "This email is not subscribed."
msgstr "구독하지 않은 이메일입니다."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "This message can not be moderated"
msgstr "이 메시지는 중재할 수 없습니다"

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_member_unique_partner
msgid "This partner is already subscribed to the group"
msgstr "이 협력사는 이미 그룹에 가입되어 있습니다"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Those messages can not be moderated: %s."
msgstr "다음 메시지들은 중재할 수 없습니다: %s."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "To Review"
msgstr "검토하기"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
#, python-format
msgid "Unsubscribe"
msgstr "구독 취소"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Unsubscribe:"
msgstr "구독 취소:"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid ""
"When people send an email to the alias of the list, they will appear here."
msgstr "사람들이 목록의 별칭으로 이메일을 보내면 여기에 표시됩니다."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Whitelist"
msgstr "화이트리스트"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__allow
msgid "Whitelisted"
msgstr "화이트리스트에 추가됨"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Wrong status (%s)"
msgstr "상태 오류 (%s)"

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_moderation_mail_group_email_uniq
msgid "You can create only one rule for a given email address in a group."
msgstr "그룹 내 지정된 이메일 주소에 대해 하나의 규칙만 생성할 수 있습니다."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr "중재한 메시지가 있습니다. 절차를 진행하십시오."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "_______________________________________________"
msgstr "_______________________________________________"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "and send an email to the author ("
msgstr "작성자에게 이메일 보내기 ("

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid "attachments"
msgstr "첨부파일"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "by"
msgstr "작성자"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. \"Newsletter\""
msgstr "예. \"뉴스레터\""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. domain.com"
msgstr "예: domain.com"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "group"
msgstr "그룹"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "has been"
msgstr "해왔음"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"members<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"
msgstr ""
"멤버<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "messages / month"
msgstr "메시지 / 월"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "more replies"
msgstr "추가 답글"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "replies"
msgstr "답글"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "subscribed to"
msgstr "구독하기"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "the list"
msgstr "목록"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "unsubscribed from"
msgstr "구독 취소"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "your email..."
msgstr "귀하의 이메일..."
