# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing_sms
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mass_mailing_sms
#. odoo-javascript
#: code:addons/mass_mailing_sms/static/src/components/sms_widget/fields_sms_widget.js:0
#, python-format
msgid " (including link trackers and opt-out link) "
msgstr ""

#. module: mass_mailing_sms
#. odoo-javascript
#: code:addons/mass_mailing_sms/static/src/components/sms_widget/fields_sms_widget.js:0
#, python-format
msgid " (including link trackers) "
msgstr ""

#. module: mass_mailing_sms
#. odoo-javascript
#: code:addons/mass_mailing_sms/static/src/components/sms_widget/fields_sms_widget.js:0
#, python-format
msgid " (including opt-out link) "
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"
msgstr ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr "إحصائيات الـ24 ساعة لـ %(mailing_type)s \"%(mailing_name)s\" "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                    <span class=\"text-muted\">\n"
"                        <i class=\"fa fa-phone\"/> Contacts\n"
"                    </span>"
msgstr ""
"<br/>\n"
"                    <span class=\"text-muted\">\n"
"                        <i class=\"fa fa-phone\"/> جهات الاتصال\n"
"                    </span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"canceled_text_sms\" invisible=\"mailing_type != 'sms'\">SMS "
"Text Message have been canceled and will not be sent.</span>"
msgstr ""
"<span name=\"canceled_text_sms\" invisible=\"mailing_type != 'sms'\">تم "
"إلغاء الرسائل النصية القصيرة ولن يتم إرسالها.</span> "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"failed_text_sms\" invisible=\"mailing_type != 'sms'\">SMS Text "
"Messages could not be delivered.</span>"
msgstr ""
"<span name=\"failed_text_sms\" invisible=\"mailing_type != 'sms'\">تعذر "
"إرسال الرسائل النصية القصيرة.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"next_departure_text\" invisible=\"mailing_type != 'sms'\">This "
"SMS marketing is scheduled for </span>"
msgstr ""
"<span name=\"next_departure_text\" invisible=\"mailing_type != 'sms'\">تمت "
"جدولة التسويق عبر الرسائل النصية القصيرة في </span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"process_text_sms\" invisible=\"mailing_type != 'sms'\">SMS Text"
" Messages are being processed.</span>"
msgstr ""
"<span name=\"process_text_sms\" invisible=\"mailing_type != 'sms'\">تتم "
"معالجة الرسائل النصية القصيرة.</span> "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"scheduled_text_sms\" invisible=\"mailing_type != 'sms'\">SMS "
"Text Messages are in queue and will be sent soon.</span>"
msgstr ""
"<span name=\"scheduled_text_sms\" invisible=\"mailing_type != "
"'sms'\">الرسائل النصية القصيرة قيد الانتظار وسوف يتم إرسالها قريباً.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"sent_sms\" invisible=\"mailing_type != 'sms'\">SMS Text "
"Messages have been sent.</span>"
msgstr ""
"<span name=\"sent_sms\" invisible=\"mailing_type != 'sms'\">تم إرسال الرسائل"
" النصية القصيرة.</span> "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<span widget=\"statinfo\">Open Recipient</span>"
msgstr "<span widget=\"statinfo\">فتح المستلم</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_kanban
msgid "<span>Valid SMS Recipients</span>"
msgstr "<span>مستلمي رسائل نصية قصيرة صالحين</span> "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears you don't have enough IAP credits. Click here to buy credits.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"                            لا تملك رصيد الوكيل المدرك للهوياً الكافي. اضغط هنا لشراء الرصيد\n"
"                        </strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears your SMS account is not registered. Click here to set up your account.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"                            حساب الرسائل النصية القصيرة الخاص بك غير مسجل. اضغط هنا لضبط حسابك.\n"
"                        </strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This SMS could not be delivered.</strong>"
msgstr "<strong>تعذر إيصال هذه الرسالة النصية القصيرة.</strong> "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This SMS could not be sent.</strong>"
msgstr "<strong>تعذر إرسال هذه الرسالة النصية القصيرة.</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This number appears to be invalid.</strong>"
msgstr "<strong>يبدو أن هذا الرقم غير صالح.</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "A/B Test"
msgstr "اختبار A/B "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__ab_testing_mailings_sms_count
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__ab_testing_mailings_sms_count
msgid "A/B Test Mailings SMS #"
msgstr "اختبار A/B للرسائل النصية القصيرة # "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr "اختبار A/B: %s "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Average of Bounced"
msgstr "متوسط عدد الرسائل المرتدة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Average of Clicked"
msgstr "متوسط عدد النقرات "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "BOUNCED (%i)"
msgstr "مرتدة (%i)"

#. module: mass_mailing_sms
#: model:utm.tag,name:mass_mailing_sms.mailing_tag_0
msgid "Bioutifoul SMS"
msgstr "رسائل نصية قصيرة رائعة "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid ""
"Blacklist through SMS Marketing unsubscribe (mailing ID: %s - model: %s)"
msgstr ""
"إدراج في القائمة السوداء عن طريق إلغاء الاشتراك من التسويق عبر الرسائل "
"النصية القصيرة (معرف المراسلة: %s - النموذج: %s) "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "مدرج في القائمة السوداء "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "الهاتف الذي تم إدراجه في القائمة السوداء هو هاتف محمول "

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.phone_blacklist_menu
msgid "Blacklisted Phone Numbers"
msgstr "أرقام الهواتف المدرجة في القائمة السوداء "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "الهاتف الذي تم إدراجه في القائمة السوداء هو هاتف "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Bounced (%)"
msgstr "المرتدة (%) "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "CLICKED (%i)"
msgstr "تم الضغط (%i) "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Campaign"
msgstr "الحملة"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.menu_email_campaigns
msgid "Campaigns"
msgstr "الحملات"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Carriage-return-separated list of phone numbers"
msgstr "قائمة بأرقام الهواتف المفصولة بسطر جديد "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Clicked (%)"
msgstr "تم النقر عليها (%) "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_code
msgid "Code"
msgstr "رمز "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid ""
"Come back once some SMS Mailings are sent to check out aggregated results."
msgstr ""
"عد مجدداً بمجرد أن يتم إرسال بعض الرسائل النصية القصيرة للتحقق من النتائج. "

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_configuration
msgid "Configuration"
msgstr "التهيئة "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_country_not_supported
msgid "Country Not Supported"
msgstr "الدولة غير مدعومة "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_registration_needed
msgid "Country-specific Registration Required"
msgstr "مطلوب التسجيل الخاص بكل بلد "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid "Create a Mailing List"
msgstr "إنشاء قائمة بريدية "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid "Create a SMS Marketing Mailing"
msgstr "إنشاء مراسلات للتسويق عبر الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid "Create a mailing contact"
msgstr "إنشاء جهة اتصال بريدية "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Date"
msgstr "التاريخ"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Discard"
msgstr "إهمال "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_duplicate
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Duplicate"
msgstr "إنشاء نسخة مطابقة "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/res_users.py:0
#, python-format
msgid "Email Marketing"
msgstr "التسويق بالبريد الإلكتروني"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Exclude Blacklisted Phone"
msgstr "استثناء أرقام الهواتف المدرجة في القائمة السوداء "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_expired
msgid "Expired"
msgstr "منتهي الصلاحية"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "نوع الفشل"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"حقل مُستخدَم لتخزين أرقام الهواتف السليمة، مما يساعد على تسريع عمليات البحث "
"والمقارنات. "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"For an Email, Subject your Recipients will see in their inbox.\n"
"                    For an SMS Text Message, internal Title of the Message."
msgstr ""
"بالنسبة إلى البريد الإلكتروني، الموضوع الذي سيراه مستلموك في صناديق البريد الوارد لديهم.\n"
"                    بالنسبة إلى الرسائل النصية القصيرة، العنوان الداخلي للرسالة. "

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_subject
msgid ""
"For an email, the subject your recipients will see in their inbox.\n"
"For an SMS, the internal title of the message."
msgstr ""
"بالنسبة إلى البريد الإلكتروني، الموضوع الذي سيراه مستلموك في صناديق البريد الوارد لديهم.\n"
"بالنسبة إلى الرسائل النصية القصيرة، العنوان الداخلي للرسالة. "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr "أعلى معدل نقر "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__id
msgid "ID"
msgstr "المُعرف"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"إذا كان رقم الهاتف السليم في القائمة السوداء، لن تستلم جهة الاتصال الرسائل "
"النصية القصيرة الجماعية من أي قائمة بعد الآن "

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid ""
"Immediately send the SMS Mailing instead of queuing up. Use at your own "
"risk."
msgstr ""
"قم بإرسال مراسلات الرسائل النصية القصيرة فوراً عوضاً عن الانتظار. استخدمه "
"على مسؤوليتك الخاصة. "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_allow_unsubscribe
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mass_sms_allow_unsubscribe
msgid "Include opt-out link"
msgstr "يشمل رابط الانسحاب "

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"يشير إلى ما إذا كان رقم الهاتف السليم المدرج في القائمة السوداء أم لا.  "
"يساعد في تمييز أي الأرقام تم إدراجها في القائمة السوداء عندما يمون هناك "
"حقلان لرقم الهاتف والهاتف المحمول في إحدى النماذج. "

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"يشير إلى ما إذا كان رقم الهاتف السليم رقم هاتف أم لا. يساعد في تمييز أي "
"الأرقام تم إدراجها في القائمة السوداء عندما يكون هناك حقلان لرقم الهاتف "
"والهاتف المحمول في أحد النماذج. "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "الرصيد غير كافٍ "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "Insufficient IAP credits"
msgstr "رصيد الوكيل المدرك للهوية غير كافٍ "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Insufficient credits"
msgstr "الرصيد غير كافٍ "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_invalid_destination
msgid "Invalid Destination"
msgstr "الوجهة غير صالحة "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid "Invalid number %s"
msgstr "الرقم غير صالح %s "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_tracker
msgid "Link SMS to mailing/sms tracking models"
msgstr "ربط الرسائل القصيرة بنماذج تتبع البريد/الرسائل القصيرة "

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.link_tracker_menu
msgid "Link Tracker"
msgstr "متتبع الرابط"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__mailing_id
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Mailing"
msgstr "البريد "

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_contact
msgid "Mailing Contact"
msgstr "جهة اتصال بريدية "

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_list
msgid "Mailing List"
msgstr "القائمة البريدية"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_contact_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_contact_menu_sms
msgid "Mailing List Contacts"
msgstr "جهات اتصال القائمة البريدية"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_list_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_list_menu_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_contacts
msgid "Mailing Lists"
msgstr "القوائم البريدية"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr "إحصائيات البريد "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_tracker__mailing_trace_id
msgid "Mailing Trace"
msgstr "أثر المراسلات "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr "نوع البريد  "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"contact directory."
msgstr ""
"تتيح لك جهات اتصال المراسلات فصل متابعي التسويق عن دليل جهات الاتصال. "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__manual
msgid "Manual"
msgstr "يدوي"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Marketing"
msgstr "التسويق"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_id
msgid "Mass Mailing"
msgstr "المراسلات الجماعية"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_ids
msgid "Mass SMS"
msgstr "الرسائل النصية القصيرة الجماعية "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "الرقم مفقود "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile
msgid "Mobile"
msgstr "الهاتف المحمول"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_search_sms
msgid "My SMS Marketing"
msgstr "التسويق عبر الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid ""
"No need to import mailing lists, you can send SMS Text Messages to contacts "
"saved in other Odoo apps."
msgstr ""
"لا داعي لاستيراد قوائم بريدية. بوسعك إرسال الرسائل النصية القصيرة إلى جهات "
"الاتصال المحفوظة في تطبيقات أودو الأخرى. "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_not_allowed
msgid "Not Allowed"
msgstr "غير مسموح "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_not_delivered
msgid "Not Delivered"
msgstr "لم يتم توصيلها "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_number
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Number"
msgstr "عدد "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid "Number %s not found"
msgstr "لم يتم العثور على الرقم %s "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_count
msgid "Number of Mass SMS"
msgstr "عدد الرسائل النصية القصيرة الجماعية "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Number(s)"
msgstr "الرقم (الأرقام) "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "Open Recipient"
msgstr "فتح المستلم "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_optout
msgid "Opted Out"
msgstr "انسحب "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Options"
msgstr "الخيارات"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "الرسائل النصية القصيرة الصادرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "تم إدراج رقم الهاتف في القائمة السوداء "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_mobile_search
msgid "Phone/Mobile"
msgstr "الهاتف/الهاتف المتحرك "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Please enter your phone number"
msgstr "يرجى إدخال رقم هاتفك "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr "تم الاستلام (%i) "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Recipients"
msgstr "المستلمين"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_rejected
msgid "Rejected"
msgstr "تم الرفض "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Report for %(expected)i %(mailing_type)s Sent"
msgstr "تم إرسال التقرير لـ %(expected)i %(mailing_type)s "

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_reporting
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_id
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_mailing__mailing_type__sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__trace_type__sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_kanban
msgid "SMS"
msgstr "الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__body_plaintext
msgid "SMS Body"
msgstr "متن الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_list__contact_count_sms
msgid "SMS Contacts"
msgstr "جهات اتصال الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "SMS Content"
msgstr "محتوى الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_id_int
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS ID"
msgstr "معرف الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/res_users.py:0
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_mailing_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_mass_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#, python-format
msgid "SMS Marketing"
msgstr "التسويق عبر الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_trace_report_action_sms
msgid "SMS Marketing Analysis"
msgstr "تحليل التسويق عبر الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "SMS Subscription"
msgstr "اشتراك الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_template_id
msgid "SMS Template"
msgstr "قالب الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "SMS Text Message"
msgstr "الرسائة النصية القصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS Trace"
msgstr "أثر الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "SMS Traces"
msgstr "آثار الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_tracker_ids
msgid "SMS Trackers"
msgstr "متتبعات الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__ab_testing_sms_winner_selection
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__ab_testing_sms_winner_selection
msgid "SMS Winner Selection"
msgstr "اختيار الفائز عن طريق الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/sms_composer.py:0
#, python-format
msgid "STOP SMS: %(unsubscribe_url)s"
msgstr "إيقاف الرسائل النصية القصيرة: %(unsubscribe_url)s "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid "Sanitized Number"
msgstr "رقم هاتف سليم "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Send"
msgstr "إرسال"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid "Send Directly"
msgstr "الإرسال مباشرة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Send Now"
msgstr "إرسال الآن"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Send SMS"
msgstr "إرسال رسالة نصية قصيرة "

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "معالج إرسال الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send Test"
msgstr "اختبار الإرسال "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send a Sample SMS"
msgstr "إرسال عينة رسالة نصية قصيرة "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send a sample SMS for testing purpose to the numbers below."
msgstr "إرسال رسائل نصية قصيرة تجريبية لأغراض الاختبار إلى العنوان أدناه. "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_server
msgid "Server Error"
msgstr "خطأ في الخادم "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_trace_ids
msgid "Statistics"
msgstr "الإحصائيات"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Status"
msgstr "الحالة"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_sms_test_action
#, python-format
msgid "Test Mailing"
msgstr "اختبار المراسلات"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_sms_test
msgid "Test SMS Mailing"
msgstr "اختبار مراسلة الرسائل النصية القصيرة "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS could not be sent to %s: %s"
msgstr "تعذر إرسال الرسالة النصية القصيرة التجريبية إلى %s: %s "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS successfully sent to %s"
msgstr "تم إرسال الرسالة النصية القصيرة التجريبية بنجاح إلى %s "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/utm.py:0
#, python-format
msgid ""
"The UTM medium '%s' cannot be deleted as it is used in some main functional "
"flows, such as the SMS Marketing."
msgstr ""
"لا يمكن حذف وسط UTM \"%s\" حيث إنه مستخدَم في بعض الوظائف الأساسية، كالتسويق"
" عبر الرسائل النصية القصيرة. "

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "The following numbers are not correctly encoded: %s"
msgstr "لم يتم ترميز الأرقام التالية بشكل صحيح: %s "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "There was an error when trying to unsubscribe"
msgstr "حدث خطأ أثناء محاولة إلغاء الاشتراك "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"لقد تم إدراج هذا الرقم في القائمة السوداء للتسويق عبر الرسائل النصية "
"القصيرة. انقر لإزالته من القائمة السوداء. "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"This will send SMS to all recipients now. Do you still want to proceed?"
msgstr ""
"سيقوم هذا بإرسال الرسالة النصية القصيرة إلى كافة المستلمين الآن. هل ترغب "
"بالاستمرار؟ "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "This will send SMS to all recipients. Do you still want to proceed?"
msgstr ""
"سيقوم هذا بإرسال الرسالة النصية القصيرة إلى كافة المستلمين. هل ترغب "
"بالاستمرار؟ "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_subject
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Title"
msgstr "العنوان"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Total Sent"
msgstr "إجمالي عدد الرسائل المرسلة"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__trace_type
msgid "Type"
msgstr "النوع"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_utm_campaign
msgid "UTM Campaign"
msgstr "حملة UTM"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_utm_medium
msgid "UTM Medium"
msgstr "وسط UTM "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "حساب غير مسجل "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "Unregistered IAP account"
msgstr "حساب الوكيل المدرك للهوية غير مسجل "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Unregistered account"
msgstr "حساب غير مسجل "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Unsubscribe me"
msgstr "إلغاء اشتراكي"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Unsupported %s for mass SMS"
msgstr "%s غير مدعوم للرسائل النصية القصيرة الجماعية "

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Valid SMS Recipients"
msgstr "مستلمي رسائل نصية قصيرة صالحين "

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Winner Selection"
msgstr "اختيار الفائز "

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid ""
"Write an appealing SMS Text Message, define recipients and track its "
"results."
msgstr "قم بكتابة رسالة نصية قصيرة جذابة، وحدد المستلمين وتتبع النتائج. "

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "صيغة الرقم خطأ "

#. module: mass_mailing_sms
#: model:utm.campaign,title:mass_mailing_sms.utm_campaign_0
msgid "XMas Promo"
msgstr "العرض الترويجي للعيد "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "e.g. Black Friday SMS coupon"
msgstr "مثال: قسيمة عبر الرسائل النصية القصيرة للجمعة السوداء "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully blacklisted"
msgstr "تم الإدراج في القائمة السوداء بنجاح "

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully removed from"
msgstr "تمت الإزالة بنجاح من "
