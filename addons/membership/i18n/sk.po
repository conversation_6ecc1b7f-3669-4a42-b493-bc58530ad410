# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* membership
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_invoiced
msgid "# Invoiced"
msgstr "# Fakturované "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_paid
msgid "# Paid"
msgstr "# Platené"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_waiting
msgid "# Waiting"
msgstr "# Čakajúce"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" "
"title=\"Period\"/><strong> From: </strong>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" "
"title=\"Period\"/><strong> Od: </strong>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"Price\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"Price\"/>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<strong> To:</strong>"
msgstr "<strong> Do:</strong>"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__associate_member
#: model:ir.model.fields,help:membership.field_res_users__associate_member
msgid ""
"A member with whom you want to associate your membership.It will consider "
"the membership state of the associated member."
msgstr ""
"Člen, s ktorým sa chcete spojiť svoje členstvo. To zváži stav členstva "
"pridruženého člena."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_line
msgid "Account Invoice line"
msgstr "Riadok faktúry účtu"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Add a description..."
msgstr "Pridať popis..."

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid "Add a new member"
msgstr "Pridajte nového člena"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__member_price
msgid "Amount for the membership"
msgstr "Poplatok za členstvo"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__associate_member_id
#: model:ir.model.fields,field_description:membership.field_res_partner__associate_member
#: model:ir.model.fields,field_description:membership.field_res_users__associate_member
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Associate Member"
msgstr "Pridružený člen"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Associated Partner"
msgstr "Pridružený partner"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Buy Membership"
msgstr "Kúpiť členstvo"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Cancel"
msgstr "Zrušené"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,field_description:membership.field_res_users__membership_cancel
msgid "Cancel Membership Date"
msgstr "Dátum zrušenia členstva"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_cancel
msgid "Cancel date"
msgstr "Zrušiť dátum"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__canceled
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__canceled
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__canceled
msgid "Cancelled Member"
msgstr "Zrušený člen"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Category"
msgstr "Kategória"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership
#: model:ir.model.fields,help:membership.field_product_template__membership
msgid "Check if the product is eligible for membership."
msgstr "Skontrolujte či je výrobok spôsobilý pre členstvo."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__company_id
#: model:ir.model.fields,field_description:membership.field_report_membership__company_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Company"
msgstr "Spoločnosť"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_marketing_config_association
msgid "Configuration"
msgstr "Konfigurácia"

#. module: membership
#: model:ir.model,name:membership.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_state
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Current Membership State"
msgstr "Aktuálny stav členstva"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_state
#: model:ir.model.fields,field_description:membership.field_res_users__membership_state
msgid "Current Membership Status"
msgstr "Súčasný stav členstva"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Customers"
msgstr "Zákazníci"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_from
#: model:ir.model.fields,help:membership.field_product_template__membership_date_from
#: model:ir.model.fields,help:membership.field_res_partner__membership_start
#: model:ir.model.fields,help:membership.field_res_users__membership_start
msgid "Date from which membership becomes active."
msgstr "Dátum od ktorého bude členstvo aktívne."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__date
msgid "Date on which member has joined the membership"
msgstr "Dátum kedy člen vstúpil do skupiny"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,help:membership.field_res_users__membership_cancel
msgid "Date on which membership has been cancelled"
msgstr "Dátum kedy bolo členstvo zrušené"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_to
#: model:ir.model.fields,help:membership.field_product_template__membership_date_to
#: model:ir.model.fields,help:membership.field_res_partner__membership_stop
#: model:ir.model.fields,help:membership.field_res_users__membership_stop
msgid "Date until which membership remains active."
msgstr "Dátum dokedy bude členstvo aktívne"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__display_name
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__display_name
#: model:ir.model.fields,field_description:membership.field_report_membership__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_earned
msgid "Earned Amount"
msgstr "Zarobená suma"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__date_to
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Date"
msgstr "Dátum ukončenia"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Membership Date"
msgstr "Dátum ukončenia členstva"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Ending Date Of Membership"
msgstr "Dátum ukončenia členstva"

#. module: membership
#: model:ir.model.constraint,message:membership.constraint_product_template_membership_date_greater
msgid "Error! Ending Date cannot be set before Beginning Date."
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Forecast"
msgstr "Odhad"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__free_member
#: model:ir.model.fields,field_description:membership.field_res_users__free_member
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__free
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__free
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__free
msgid "Free Member"
msgstr "Bezplatný člen"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_from
msgid "From"
msgstr "Od"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Group by..."
msgstr "Zoskupiť podľa..."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__id
#: model:ir.model.fields,field_description:membership.field_report_membership__id
msgid "ID"
msgstr "ID"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Inactive"
msgstr "Neaktívne"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_id
msgid "Invoice"
msgstr "Faktúra"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Invoice Membership"
msgstr "Vyfaktúrovať členstvo"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__invoiced
msgid "Invoiced Member"
msgstr "Vyfaktúrovaný člen"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Invoiced/Paid/Free"
msgstr "Vyfaktúrované/Zaplatené/Zdarma"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_state
#: model:ir.model.fields,help:membership.field_res_users__membership_state
msgid ""
"It indicates the membership state.\n"
"-Non Member: A partner who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paying member: A member who has paid the membership fee."
msgstr ""
"To indikuje stav členstva.\n"
"-Nečlen: Partner, ktorý nepožiadal o akékoľvek členstvo.\n"
"-Zrušený člen: Člen, ktorý zrušil svoje členstvo.\n"
"-Bývalý člen: Člen, ktorého dátum členstva vypršal.\n"
"-Čakajúci člen: Člen, ktorý požiadal o členstvo a ktorého faktúra bude vytvorená.\n"
"-Vyfaktúrovaný člen: Člen, ktorého faktúra bola vytvorená.\n"
"-Platiaci člen: Člen, ktorý zaplatil členský poplatok."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__state
msgid ""
"It indicates the membership status.\n"
"-Non Member: A member who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paid Member: A member who has paid the membership amount."
msgstr ""
"Označuje stav členstva.\n"
"- Nie je členom: Člen, ktorý nepožiadal o členstvo.\n"
"- Zrušený člen: člen, ktorý zrušil svoje členstvo.\n"
"- Starý člen: Člen, ktorého dátum členstva vypršal.\n"
"- Čakajúci člen: Člen, ktorý požiadal o členstvo a ktorého faktúra sa má vytvoriť.\n"
"- Fakturovaný člen: člen, ktorého faktúra bola vytvorená.\n"
"- Platený člen: Člen, ktorý zaplatil sumu členstva."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date
msgid "Join Date"
msgstr "Dátum pridania"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_invoice_view
msgid "Join Membership"
msgstr "Pripojte sa k členstvu"

#. module: membership
#: model:ir.model,name:membership.model_account_move
msgid "Journal Entry"
msgstr "Vstup účtovnej knihy"

#. module: membership
#: model:ir.model,name:membership.model_account_move_line
msgid "Journal Item"
msgstr "Položka účtovnej knihy"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__partner_id
msgid "Member"
msgstr "Člen"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__member_price
msgid "Member Price"
msgstr "Cena člena"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_members
#: model:ir.ui.menu,name:membership.menu_association
#: model:ir.ui.menu,name:membership.menu_membership
#: model_terms:ir.ui.view,arch_db:membership.membership_members_tree
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Members"
msgstr "Členovia"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_report_membership_tree
msgid "Members Analysis"
msgstr "Analýza člena"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__product_id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__membership_id
#: model:ir.model.fields,field_description:membership.field_product_product__membership
#: model:ir.model.fields,field_description:membership.field_product_template__membership
#: model:ir.model.fields,field_description:membership.field_res_partner__member_lines
#: model:ir.model.fields,field_description:membership.field_res_users__member_lines
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_graph1
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_pivot
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership"
msgstr "Členstvo"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_amount
#: model:ir.model.fields,field_description:membership.field_res_users__membership_amount
msgid "Membership Amount"
msgstr "Suma členstva"

#. module: membership
#: model:ir.model,name:membership.model_report_membership
msgid "Membership Analysis"
msgstr "Analýza členstva"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Membership Duration"
msgstr "Trvanie členstva"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_to
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_to
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_stop
#: model:ir.model.fields,field_description:membership.field_res_users__membership_stop
msgid "Membership End Date"
msgstr "Dátum ukončenia členstva"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__member_price
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership Fee"
msgstr "Členský poplatok"

#. module: membership
#: model:ir.model,name:membership.model_membership_invoice
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Membership Invoice"
msgstr "Faktúra členstva"

#. module: membership
#: model:ir.model,name:membership.model_membership_membership_line
msgid "Membership Line"
msgstr "Členská línia"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership Partners"
msgstr "Partneri členstva"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_id
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership Product"
msgstr "Produkt členstva"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_products
#: model:ir.ui.menu,name:membership.menu_membership_products
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Membership Products"
msgstr "Produkty členstva"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_from
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_from
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_start
#: model:ir.model.fields,field_description:membership.field_res_users__membership_start
msgid "Membership Start Date"
msgstr "Dátum začiatku členstva"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership State"
msgstr "Stav členstva"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__state
msgid "Membership Status"
msgstr "Status členstva"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership products"
msgstr "Produkty členstva"

#. module: membership
#: model:ir.actions.server,name:membership.ir_cron_update_membership_ir_actions_server
msgid "Membership: update memberships"
msgstr "Členstvo: aktualizujte členstvo"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Memberships"
msgstr "Členstvá"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Month"
msgstr "Mesiac"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_report_membership_tree
msgid "No data yet!"
msgstr "Zatiaľ žiadne údaje!"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__none
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__none
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__none
msgid "Non Member"
msgstr "Nečlen"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid ""
"Odoo helps you easily track all activities related to a member: \n"
"                  Current Membership Status, Discussions and History of Membership, etc."
msgstr ""
"Odoo vám pomôže ľahko sledovať všetky aktivity súvisiace s členom:\n"
"Aktuálny stav členstva, diskusie a história členstva, atď."

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__old
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__old
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__old
msgid "Old Member"
msgstr "Starý člen"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__paid
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__paid
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__paid
msgid "Paid Member"
msgstr "Platba členského"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__partner
msgid "Partner"
msgstr "Partner"

#. module: membership
#. odoo-python
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner doesn't have an address to make the invoice."
msgstr "Partner nemá adresu na vytvorenie faktúry."

#. module: membership
#. odoo-python
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner is a free Member."
msgstr "Partner má bezplatné členstvo"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_pending
msgid "Pending Amount"
msgstr "Očakávaná suma"

#. module: membership
#: model:ir.model,name:membership.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Product Name"
msgstr "Názov produktu"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__quantity
msgid "Quantity"
msgstr "Množstvo"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_report_membership
msgid "Reporting"
msgstr "Prehľady"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Revenue Done"
msgstr "Výnos hotový"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__user_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Salesperson"
msgstr "Obchodník"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__free_member
#: model:ir.model.fields,help:membership.field_res_users__free_member
msgid "Select if you want to give free membership."
msgstr "Vyberte ak chcete dať bezplatné členstvo."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__start_date
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Start Date"
msgstr "Dátum začiatku"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Starting Date Of Membership"
msgstr "Začiatočný dátum členstva"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of # Invoiced"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of # Paid"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of Earned Amount"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of Quantity"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Taxes"
msgstr "Dane"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_amount
#: model:ir.model.fields,help:membership.field_res_users__membership_amount
msgid "The price negotiated by the partner"
msgstr "Cena vyjednaná partnerom"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "This note will be displayed on quotations..."
msgstr "Táto poznámka bude zobrazená na cenových ponukách..."

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display paid, old and total earned columns"
msgstr "Toto zobrazí zaplatené, staré a celkovo zarobené stĺpce"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display waiting, invoiced and total pending columns"
msgstr "Toto zobrazí čakajúce, vyfaktúrované a celkovo čakajúce stĺpce"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_to
msgid "To"
msgstr "Do"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Vendors"
msgstr "Dodávatelia"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__waiting
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__waiting
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__waiting
msgid "Waiting Member"
msgstr "Čakajúci člen"

#. module: membership
#. odoo-python
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "You cannot create recursive associated members."
msgstr "Nemôžete vytvoriť rekurzívnych pridružených členov."
