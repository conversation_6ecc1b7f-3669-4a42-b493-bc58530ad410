# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onboarding
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON>il <PERSON>do<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:29+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "#{alt}"
msgstr "#{alt}"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__progress_ids
msgid "All Onboarding Progress Records (across companies)."
msgstr "บันทึกความคืบหน้าการเริ่มต้นใช้งานทั้งหมด (ระหว่างบริษัท)"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "All done!"
msgstr "เสร็จสิ้น"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "All related Onboarding Progress Step Records (across companies)"
msgstr ""
"บันทึกขั้นตอนความคืบหน้าการเริ่มต้นใช้งานที่เกี่ยวข้องทั้งหมด "
"(ระหว่างบริษัท)"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image_alt
msgid "Alt Text for the Step Image"
msgstr "ข้อความ Alt สำหรับรูปภาพขั้นตอน"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
#, python-format
msgid ""
"An \"Opening Action\" is required for the following steps to be linked to an"
" onboarding panel: %(step_titles)s"
msgstr ""
"จำเป็นต้องมี \"การดำเนินการเปิด\" "
"สำหรับขั้นตอนต่อไปนี้เพื่อเชื่อมโยงกับแผงการเริ่มต้นใช้งาน: %(step_titles)s"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Are you sure you want to hide these configuration steps?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการซ่อนขั้นตอนการกำหนดค่าเหล่านี้"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__button_text
msgid "Button text"
msgstr "ข้อความของปุ่ม"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Cancel"
msgstr "ยกเลิก"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close"
msgstr "ปิด"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close Panel"
msgstr "ปิดแผงควบคุม"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close the onboarding panel"
msgstr "ปิดแผงการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Closing action"
msgstr "ปิดการดำเนินการ"

#. module: onboarding
#: model:ir.model,name:onboarding.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__company_id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__company_id
msgid "Company"
msgstr "บริษัท"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_onboarding_state
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_step_state
msgid "Completion State"
msgstr "สถานะเสร็จสมบูรณ์"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__description
msgid "Description"
msgstr "คำอธิบาย"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__done
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_icon
msgid "Font Awesome Icon when completed"
msgstr "ไอคอนฟ้อนต์ที่ยอดเยี่ยมเมื่อสร้างเสร็จ"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Get them out of my sight!"
msgstr "นำออกไปให้พ้นสายตา!"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Hide Onboarding Tips"
msgstr "ซ่อนเคล็ดลับการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__id
msgid "ID"
msgstr "ไอดี"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__is_per_company
msgid "Is per company"
msgstr "เป็นรายบริษัท"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__just_done
msgid "Just done"
msgstr "สําเร็จแล้ว"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
#, python-format
msgid "Let's do it"
msgstr "ลุยกันเลย"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__text_completed
msgid "Message at completion"
msgstr "ข้อความเมื่อเสร็จสิ้น"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__name
msgid "Name of the onboarding"
msgstr "ชื่อของการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Name of the onboarding model action to execute when closing the panel."
msgstr ""
"ชื่อของการดำเนินการโมเดลการเริ่มต้นใช้งานที่จะดำเนินการเมื่อปิดแผงควบคุม"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid ""
"Name of the onboarding step model action to execute when opening the step, "
"e.g. action_open_onboarding_1_step_1"
msgstr ""
"ชื่อของการดำเนินการโมเดลขั้นตอนการเริ่มต้นใช้งานที่จะดำเนินการเมื่อเปิดขั้นตอน"
" เช่น action_open_onboarding_1_step_1"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding.py:0
#, python-format
msgid "Nice work! Your configuration is done."
msgstr "ทำได้ดีมาก! การกำหนดค่าของคุณเสร็จสิ้นแล้ว"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__not_done
msgid "Not done"
msgstr "ยังไม่เสร็จ"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding
msgid "Onboarding"
msgstr "การเริ่มงาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress"
msgstr "ความคืบหน้าการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__progress_ids
msgid "Onboarding Progress Records"
msgstr "บันทึกความคืบหน้าการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "Onboarding Progress Step Records"
msgstr "บันทึกขั้นตอนความคืบหน้าการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress_step
msgid "Onboarding Progress Step Tracker"
msgstr "ตัวติดตามขั้นตอนความคืบหน้าการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Onboarding Progress Step for the current context (company)."
msgstr "ขั้นตอนความคืบหน้าในการเตรียมความพร้อมสำหรับบริบทปัจจุบัน (บริษัท)"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress
msgid "Onboarding Progress Tracker"
msgstr "เครื่องมือติดตามความคืบหน้าการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress for the current context (company)."
msgstr "ความคืบหน้าในการเตรียมความพร้อมสำหรับบริบทปัจจุบัน (บริษัท)"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding_step
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_id
msgid "Onboarding Step"
msgstr "ขั้นตอนการเริ่มใช้งาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_state
msgid "Onboarding Step Progress"
msgstr "ความคืบหน้าในการเริ่มใช้งาน"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_step
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_tree
msgid "Onboarding Steps"
msgstr "ขั้นตอนการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_onboarding_route_name_uniq
msgid "Onboarding alias must be unique."
msgstr "นามแฝงการเริ่มต้นใช้งานจะต้องไม่ซ้ำกัน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_state
msgid "Onboarding progress"
msgstr "ความคืบหน้าการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__step_ids
msgid "Onboarding steps"
msgstr "ขั้นตอนการเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__onboarding_ids
#: model:ir.ui.menu,name:onboarding.menu_onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Onboardings"
msgstr "การเริ่มต้นใช้งาน"

#. module: onboarding
#: model:ir.ui.menu,name:onboarding.menu_onboarding_step
msgid "Onboardings Steps"
msgstr "ขั้นตอนการเริ่มใช้งาน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__route_name
msgid "One word name"
msgstr "ชื่อคำเดียว"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid "Opening action"
msgstr "กำลังเปิดการดำเนินการ"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__progress_step_ids
msgid "Progress Steps Trackers"
msgstr "ตัวติดตามขั้นตอนความคืบหน้า"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__progress_ids
msgid "Related Onboarding Progress Tracker"
msgstr "ตัวติดตามความคืบหน้าการเริ่มต้นใช้งานที่เกี่ยวข้อง"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_id
msgid "Related onboarding tracked"
msgstr "ติดตามการเริ่มต้นใช้งานที่เกี่ยวข้อง"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__sequence
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_per_company
msgid "Should be done per company?"
msgstr "ควรทำทีละบริษัทหรือไม่?"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__step_image_alt
msgid "Show when impossible to load the image"
msgstr "แสดงเมื่อไม่สามารถโหลดภาพได้"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Step Completed!"
msgstr "ขั้นตอนเสร็จสมบูรณ์!"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image
msgid "Step Image"
msgstr "รูปภาพขั้นตอน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image_filename
msgid "Step Image Filename"
msgstr "ชื่อไฟล์รูปภาพขั้นตอน"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Step Progress"
msgstr "ความคืบหน้าขั้นตอน"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
msgid "Steps"
msgstr "สถานะ"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__button_text
msgid "Text on the panel's button to start this step"
msgstr "ข้อความบนปุ่มของแผงเพื่อเริ่มขั้นตอนนี้"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__text_completed
msgid "Text shown on onboarding when completed"
msgstr "ข้อความที่แสดงเมื่อเริ่มต้นใช้งานเมื่อเสร็จสิ้น"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_text
msgid "Text to show when step is completed"
msgstr "ข้อความที่จะแสดงเมื่อขั้นตอนเสร็จสิ้น"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__title
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Toggle visibility"
msgstr "สลับการมองเห็น"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_onboarding_closed
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__is_onboarding_closed
msgid "Was panel closed?"
msgstr "แผงถูกปิดใช่หรือไม่?"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "o_onboarding_confetti"
msgstr "o_onboarding_confetti"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_panel
msgid "onboarding.onboarding.step"
msgstr "onboarding.onboarding.step"
