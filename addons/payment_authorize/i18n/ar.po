# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid ""
"<select id=\"o_authorize_account_type\" required=\"\" class=\"form-select\">\n"
"                        <option value=\"checking\">Personal Checking</option>\n"
"                        <option value=\"savings\">Personal Savings</option>\n"
"                        <option value=\"businessChecking\">Business Checking</option>\n"
"                    </select>"
msgstr ""
"<select id=\"o_authorize_account_type\" required=\"\" class=\"form-select\">\n"
"                        <option value=\"checking\">حساب جاري شخصي</option>\n"
"                        <option value=\"savings\">حساب توفير شخصي</option>\n"
"                        <option value=\"businessChecking\">حساب جاري للأعمال</option>\n"
"                    </select>"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "رقم مسار ABA "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_client_key
msgid "API Client Key"
msgstr "مفتاح عميل API "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_login
msgid "API Login ID"
msgstr "معرف تسجيل دخول API  "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_signature_key
msgid "API Signature Key"
msgstr "مفتاح توقيع API "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_transaction_key
msgid "API Transaction Key"
msgstr "مفتاح معاملة واجهة برمجة التطبيقات API"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "رقم الحساب"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_provider__code__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "معرف الملف التعريفي لـ Authorize.Net "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "نوع الحساب البنكي "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "اسم البنك"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "رمز البطاقة "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "رقم البطاقة"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__code
msgid "Code"
msgstr "رمز "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
#, python-format
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
"تعذر إحضار بيانات التاجر: \n"
"%s "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid ""
"Could not retrieve the transaction details. (error code: %s; error_details: "
"%s)"
msgstr "تعذر جلب تفاصيل المعاملة. (كود الخطأ: %s؛ rror_details: %s) "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "تاريخ الانتهاء "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
#, python-format
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"فشلت المصادقة. \n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Generate Client Key"
msgstr "إنشاء مفتاح العميل "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "How to get paid with Authorize.Net"
msgstr "كيفية تلقي أموال من خلال Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "الاسم في الحساب "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "لم يتم العثور على معاملة تطابق المرجع %s. "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
#, python-format
msgid "Only one currency can be selected by Authorize.Net account."
msgstr "يمكن تحديد عملة واحدة فقط في حساب Authorize.Net. "

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_provider
msgid "Payment Provider"
msgstr "مزود الدفع "

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "كلمة سر السداد"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة السداد"

#. module: payment_authorize
#. odoo-javascript
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "فشلت معالجة عملية الدفع "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr "البيانات المستلمة مع الحالة \"%(status)s\" وكود الخطأ \"%(error)s\" "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "بيانات طلب الدفع المستلمة المتلاعب بها. "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Set Account Currency"
msgstr "تعيين عملة الحساب "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr "المعرف مُستخدم فقط لتعريف الحساب مع Authorize.Net "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"مفتاح العميل العام. للإنشاء مباشرة من أودو أو من الواجهة الخلفية لـ "
"Authorize.Net. "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "الكود التقني لمزود الدفع هذا. "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction is not in a status to be refunded. (status: %s, details: %s)"
msgstr "المعاملة ليست في حالة تسمح برد الأموال. (الحالة: %s، التفاصيل: %s) "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "المعاملة غير مرتبطة برمز. "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"المرجع الفريد للمجموع بين الشريك/الرمز في الواجهة الخلفية لـ Authorize.net. "

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
#, python-format
msgid "This action cannot be performed while the provider is disabled."
msgstr "لا يمكن تنفيذ هذا الإجراء بينما مزود الدفع معطل. "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "YY"
