# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_flutterwave
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during the processing of your payment (status %s). Please "
"try again."
msgstr ""
"Ocorreu um erro durante o processamento do seu pagamento (status %s). Tente "
"novamente."

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__code
msgid "Code"
msgstr "Código"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Não foi possível estabelecer a conexão com a API."

#. module: payment_flutterwave
#: model:ir.model.fields.selection,name:payment_flutterwave.selection__payment_provider__code__flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_token__flutterwave_customer_email
msgid "Flutterwave Customer Email"
msgstr "Flutterwave – E-mail do cliente"

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_public_key
msgid "Flutterwave Public Key"
msgstr "Flutterwave – Chave pública"

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_secret_key
msgid "Flutterwave Secret Key"
msgstr "Flutterwave – Chave secreta"

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_webhook_secret
msgid "Flutterwave Webhook Secret"
msgstr "Flutterwave – Webhook secreto"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Nenhuma transação encontrada com a referência %s."

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_provider
msgid "Payment Provider"
msgstr "Provedor de serviços de pagamento"

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_token
msgid "Payment Token"
msgstr "Token de pagamento"

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transação de pagamento"

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Public Key"
msgstr "Chave pública"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference."
msgstr "Dados recebidos com referência ausente."

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Secret Key"
msgstr "Chave secreta"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_provider.py:0
#, python-format
msgid ""
"The communication with the API failed. Flutterwave gave us the following "
"information: '%s'"
msgstr ""
"A comunicação com a API falhou. O Flutterwave nos forneceu as seguintes "
"informações '%s'"

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_token__flutterwave_customer_email
msgid "The email of the customer at the time the token was created."
msgstr "O e-mail do cliente no momento em que o token foi criado."

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_provider__flutterwave_public_key
msgid "The key solely used to identify the account with Flutterwave."
msgstr ""
"A chave usada exclusivamente para identificar a conta com o Flutterwave."

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "O código técnico deste provedor de pagamento."

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "A transação não está vinculada a um token."

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
#, python-format
msgid "Unknown payment status: %s"
msgstr "Status de pagamento desconhecido: %s"

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Webhook Secret"
msgstr "Segredo do webhook"
