# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_paypal
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-06 08:29+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Georgian (https://www.transifex.com/odoo/teams/41243/ka/)\n"
"Language: ka\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"<br/><br/>\n"
"                Thanks,<br/>\n"
"                <b>The Odoo Team</b>"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
#, python-format
msgid "Add your PayPal account to Odoo"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__code
msgid "Code"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_email_account
msgid "Email"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"Hello,\n"
"                <br/><br/>\n"
"                You have received a payment through PayPal.<br/>\n"
"                Kindly follow the instructions given by PayPal to create your account.<br/>\n"
"                Then, help us complete your Paypal credentials in Odoo.<br/><br/>"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "How to configure your paypal account?"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_seller_account
msgid "Merchant Account ID"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_pdt_token
msgid "PDT Identity Token"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_provider__code__paypal
msgid "Paypal"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid "Set Paypal credentials"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_email_account
msgid "The public business email solely used to identify the account with PayPal"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid "The status of transaction with reference %(ref)s was not synchronized because the 'Payment data transfer' option is not enabled on the PayPal dashboard."
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid "The status of transaction with reference %(ref)s was not synchronized because the PDT Identify Token is not configured on the provider %(record_link)s."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_use_ipn
msgid "Use IPN"
msgstr ""
