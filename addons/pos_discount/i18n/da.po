# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_discount
#. odoo-python
#: code:addons/pos_discount/models/pos_config.py:0
#, python-format
msgid ""
"A discount product is needed to use the Global Discount feature. Go to Point"
" of Sale > Configuration > Settings to set it."
msgstr ""
"Et rabatprodukt er nødvendig for at bruge Global Rabat-funktionen. Gå til "
"Point of Sale > Konfiguration > Indstillinger for at indstille det."

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "Tillad ekspedienten at give rabat på hele ordren."

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.xml:0
#, python-format
msgid "Discount"
msgstr "Rabat"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr "Rabat %"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "Procentvis rabat"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "Giv rabat på et produkt"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid "No discount product found"
msgstr "Der blev ikke fundet noget rabatprodukt"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid "No tax"
msgstr "Ingen moms"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "Ordrerabatter"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS konfiguration"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_session
msgid "Point of Sale Session"
msgstr "PoS session"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr "Pos rabatprodukt"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid "Tax: %s"
msgstr "Moms: %s"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr "Standard rabatprocent, når der klikkes på Rabat-knappen"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr ""
"Rabatproduktet virker forkert konfigureret. Sørg for, at det er markeret som"
" 'Kan sælges' og 'Tilgængeligt i POS'."

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr "Produktet brugt til at anvende rabatten på billetten."
