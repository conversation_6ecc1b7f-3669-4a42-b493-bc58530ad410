# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_discount
#. odoo-python
#: code:addons/pos_discount/models/pos_config.py:0
#, python-format
msgid ""
"A discount product is needed to use the Global Discount feature. Go to Point"
" of Sale > Configuration > Settings to set it."
msgstr "要使用全局折扣功能，需要有一个折扣产品。进入销售点 > 配置 > 设置来设置它。"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "允许收银员在整张订单给出折扣."

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.xml:0
#, python-format
msgid "Discount"
msgstr "折扣"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr "折扣%"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "折扣百分比"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "折扣产品"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid "No discount product found"
msgstr "没有找到折扣商品"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid "No tax"
msgstr "无税"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "订单折扣"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS配置"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_session
msgid "Point of Sale Session"
msgstr "POS会话"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr "POS折扣产品"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid "Tax: %s"
msgstr "税率：%s"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr "点击折扣按钮时的默认折扣百分比"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr "折扣商品配置错误。请确认折扣商品被标记为可销售并且POS可用。"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr "用来在票面上应用折扣的产品。"
