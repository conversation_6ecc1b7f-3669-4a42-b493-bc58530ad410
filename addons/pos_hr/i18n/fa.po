# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# far<PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Advanced rights"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "All employees"
msgstr "کل کارکنان"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Basic rights"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "صندوقدار"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Change Cashier"
msgstr "تغییر صندوق‌دار"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "کارمند"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "کارمند: %s - پیکربندی‌(های) نقطه فروش: %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "Employees with basic access"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "Employees with manager access"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "If left empty, all employees can log in to PoS"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "If left empty, only Odoo users have extended rights in PoS"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Incorrect Password"
msgstr "گذرواژه نادرست"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/navbar/navbar.xml:0
#, python-format
msgid "Lock"
msgstr "قفل"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Log in to"
msgstr "ورود به"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "No Cashiers"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "No employee"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Password?"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"شخصی که از صندوق پول استفاده می کند. این می تواند یک دانش آموز یا یک کارمند "
"موقت باشد."

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Please try again."
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "پیکربندی پایانه فروش"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "سفارشات پایانه فروش"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "گزارش سفارشات پایانه فروش"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_session
msgid "Point of Sale Session"
msgstr "نشست پایانه فروش"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Scan your badge"
msgstr "نشان خود را اسکن کنید"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Select Cashier"
msgstr "انتخاب صندوق‌دار"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "There are no employees to select as cashier. Please create one."
msgstr ""

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"نمی‌توانید کارمندی که ممکن است شیفت فعال داشته باشد حذف کنید ابتدا شیفت(ها) "
"را ببندید: \n"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "or"
msgstr "یا"
