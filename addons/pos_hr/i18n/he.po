# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# שהאב חו<PERSON>יין <<EMAIL>>, 2023
# <PERSON>el Nahmany, 2024
# tomerlayline, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: tomerlayline, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Advanced rights"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "All employees"
msgstr "כל העובדים"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Basic rights"
msgstr "זכויות בסיסיות"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "קופאי"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Change Cashier"
msgstr "שנה קופאי"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "עובד"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "עובדים: %s - תצורות קופה: %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "Employees with basic access"
msgstr "עובדים עם גישה בסיסית"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "Employees with manager access"
msgstr "עובדים עם גישת מנהל"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "If left empty, all employees can log in to PoS"
msgstr "אם נשאר ריק, כל העובדים יכולים להתחבר לקופה"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "If left empty, only Odoo users have extended rights in PoS"
msgstr "אם נשאר ריק, רק משתמשי Odoo יהיו בעלי זכויות מורחבות בקופה"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Incorrect Password"
msgstr "סיסמה לא נכונה"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/navbar/navbar.xml:0
#, python-format
msgid "Lock"
msgstr "Lock"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Log in to"
msgstr "התחבר ל"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "No Cashiers"
msgstr "אין קופאים"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "No employee"
msgstr ""

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Password?"
msgstr "סיסמה?"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr "האדם המשתמש בקופה.יכול להיות מחליף, עובד זמני או מתלמד."

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Please try again."
msgstr "בבקשה נסה שוב."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "תצורת קופה"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "הזמנות קופה"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "דוח הזמנות קופה"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_session
msgid "Point of Sale Session"
msgstr "משמרת קופה "

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Scan your badge"
msgstr "סרוק את התג שלך"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Select Cashier"
msgstr "בחר קופאי"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "There are no employees to select as cashier. Please create one."
msgstr ""

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr "אינך יכול למחוק עובד שדרוש למשמרת קופה פעילה, סגור תחילה את המשמרות: \n"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "or"
msgstr "או"
