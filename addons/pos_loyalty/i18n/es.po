# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_loyalty
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid ""
"\n"
"        Technical field used to link multiple reward lines from the same reward together.\n"
"    "
msgstr ""
"\n"
"        Campo técnico que se usa para vincular varias líneas de recompensa en una sola recompensa.\n"
"    "

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/partner_line/partner_line.js:0
#, python-format
msgid "%s Points"
msgstr "%s puntos"

#. module: pos_loyalty
#: model:loyalty.program,name:pos_loyalty.15_pc_on_next_order
msgid "15% on next order"
msgstr "15% en el próximo pedido"

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.15_pc_on_next_order_reward
msgid "15% on your order"
msgstr "15% en su pedido"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "A better global discount is already applied."
msgstr "Ya se aplicó un mejor descuento global."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
#, python-format
msgid "A reward could not be loaded"
msgstr "No se ha podido cargar una recompensa "

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__promo_barcode
msgid ""
"A technical field used as an alternative to the promo code. This is "
"automatically generated when the promo code is changed."
msgstr ""
"Campo técnico utilizado como alternativa al código promocional. Se genera "
"automáticamente cuando se cambia el código promocional."

#. module: pos_loyalty
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "All PoS"
msgstr "Todos los TPV"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__any_product
msgid "Any Product"
msgstr "Cualquier producto"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.js:0
#, python-format
msgid ""
"Are you sure you want to remove %s from this order?\n"
" You will still be able to claim it through the reward button."
msgstr ""
"¿Está seguro de que quiere eliminar %s de este pedido? Podrá seguir "
"reclamándolo a través del botón de recompensa."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#, python-format
msgid "Balance"
msgstr "Saldo"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__promo_barcode
#, python-format
msgid "Barcode"
msgstr "Código de barras"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_barcode_rule
msgid "Barcode Rule"
msgstr "Regla de código de barras"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__coupon_id
#: model:ir.model.fields.selection,name:pos_loyalty.selection__barcode_rule__type__coupon
msgid "Coupon"
msgstr "Cupón"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#, python-format
msgid "Coupon Codes"
msgstr "Códigos de cupones"

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.15_pc_on_next_order
msgid "Coupon point(s)"
msgstr "Punto(s) de cupón"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Current Balance:"
msgstr "Saldo actual:"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "Customer needed"
msgstr "Cliente requerido"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.js:0
#, python-format
msgid "Deactivating reward"
msgstr "Desactivación de la recompensa"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_config__gift_card_settings
#: model:ir.model.fields,help:pos_loyalty.field_res_config_settings__pos_gift_card_settings
msgid "Defines the way you want to set your gift cards."
msgstr "Define como desea establecer sus tarjetas de regalo."

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_discount_loyalty_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Discount & Loyalty"
msgstr "Descuento y fidelidad"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/promo_code_button/promo_code_button.js:0
#: code:addons/pos_loyalty/static/src/app/control_buttons/promo_code_button/promo_code_button.xml:0
#, python-format
msgid "Enter Code"
msgstr "Ingresar código"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
#, python-format
msgid "Enter the gift card code"
msgstr "Ingrese el código de la tarjeta de regalo"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Error validating rewards"
msgstr "Error validando recompensa"

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.loyalty_program_reward
msgid "Free Product - Simple Pen"
msgstr "Producto gratis - Bolígrafo simple"

#. module: pos_loyalty
#: model:ir.model.fields.selection,name:pos_loyalty.selection__pos_config__gift_card_settings__create_set
msgid "Generate PDF cards"
msgstr "Generar tarjetas en PDF"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
#, python-format
msgid "Generate a Gift Card"
msgstr "Generar una tarjeta de regalo"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid ""
"Gift Card: %s\n"
"Balance: %s"
msgstr ""
"Tarjeta de regalo: %s\n"
"Saldo: %s"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_config__gift_card_settings
#: model:ir.model.fields,field_description:pos_loyalty.field_res_config_settings__pos_gift_card_settings
msgid "Gift Cards settings"
msgstr "Ajustes de tarjetas de regalo"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/promo_code_button/promo_code_button.js:0
#, python-format
msgid "Gift card or Discount code"
msgstr "Tarjeta de regalo o código de descuento"

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_gift_ewallet_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Gift cards & eWallet"
msgstr "Tarjetas de regalo y monedero electrónico"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__points_cost
msgid "How many point this reward cost on the coupon."
msgstr "Cuántos puntos cuesta esta recompensa en el cupón."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program reward. Use 1 currency per point discount."
msgstr ""
"Recompensa del programa de tarjeta regalo no válida. Utilice una moneda por "
"punto de descuento."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program rule. Use 1 point per currency spent."
msgstr ""
"Regla del programa de tarjetas de regalo no válida. Use 1 punto por moneda "
"gastada."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program. More than one reward."
msgstr "Programa de tarjetas de regalo no válido. Hay más de una recompensa."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program. More than one rule."
msgstr "Programa de tarjetas de regalo no válido. Hay más de una regla."

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Is Reward Line"
msgstr "Es línea de recompensa"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr "Comunicación de fidelización"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Cupón de fidelidad"

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.loyalty_program
msgid "Loyalty Points"
msgstr "Puntos de fidelidad"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_program
#: model:loyalty.program,name:pos_loyalty.loyalty_program
msgid "Loyalty Program"
msgstr "Programa de fidelidad"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Recompensa de fidelidad"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Regla de fidelidad"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "New Total"
msgstr "Nuevo total"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.js:0
#, python-format
msgid "No"
msgstr "No"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "No reward can be claimed with this coupon."
msgstr "No se puede reclamar ninguna recompensa con este cupón."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid "No valid eWallet found"
msgstr "No se encontró ningún monedero electrónico válido"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/reward_button/reward_button.js:0
#, python-format
msgid "Please select a product for this reward"
msgstr "Seleccione un producto para esta recompensa"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/reward_button/reward_button.js:0
#, python-format
msgid "Please select a reward"
msgstr "Seleccione una recompensa"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_order_count
msgid "PoS Order Count"
msgstr "Número de pedidos de TPV"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS Order Reference"
msgstr "Referencia de pedido de TPV"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS order where this coupon was generated."
msgstr "Pedido del TPV donde se generó este cupón."

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_ok
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "Point of Sale"
msgstr "Punto de venta"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuración del TPV"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Líneas de pedido de punto de venta"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order
msgid "Point of Sale Orders"
msgstr "Pedidos del TPV"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesión TPV"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Point of Sales"
msgstr "Terminal punto de venta (TPV)"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "Points"
msgstr "Puntos"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Points Balance"
msgstr "Saldo de puntos"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__points_cost
msgid "Points Cost"
msgstr "Coste por puntos"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Points Spent"
msgstr "Puntos gastados"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "Points Won"
msgstr "Puntos ganados"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_mail__pos_report_print_id
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "Print Report"
msgstr "Imprimir informe"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s, Reward Product: `%(reward_product)s`"
msgstr "Programa: %(name)s, Producto de recompensa: `%(reward_product)s`"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s, Rule Product: `%(rule_product)s`"
msgstr "Programa: %(name)s, Regla de producto: `%(rule_product)s`"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid "Refund with eWallet"
msgstr "Reembolso con monedero electrónico"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"Refunding a top up or reward product for an eWallet or gift card program is "
"not allowed."
msgstr ""
"No está permitido reembolsar una recarga o un producto de recompensa para un"
" programa de monedero electrónico o tarjeta de regalo."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/reset_programs_button/reset_programs_button.xml:0
#, python-format
msgid "Reset Programs"
msgstr "Restablecer programas"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Restrict publishing to those shops."
msgstr "Restringir la publicación a esas tiendas."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/reward_button/reward_button.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_id
#, python-format
msgid "Reward"
msgstr "Recompensa"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid "Reward Identifier Code"
msgstr "Código de identificación de la recompensa"

#. module: pos_loyalty
#: model:ir.model.fields.selection,name:pos_loyalty.selection__pos_config__gift_card_settings__scan_use
msgid "Scan existing cards"
msgstr "Escanear tarjetas existentes"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
#, python-format
msgid "Select program"
msgstr "Seleccionar programa"

#. module: pos_loyalty
#: model:product.template,name:pos_loyalty.simple_pen_product_template
msgid "Simple Pen"
msgstr "Bolígrafo simple"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid ""
"Some coupons are invalid. The applied coupons have been updated. Please "
"check the order."
msgstr ""
"Algunos cupones no son válidos. Los cupones aplicados han sido actualizados."
" Por favor, compruebe el pedido."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#, python-format
msgid "Spent:"
msgstr "Gastado:"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__any_product
msgid "Technical field, whether all product match"
msgstr "Campo técnico, si todos los productos coinciden"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "That coupon code has already been scanned and activated."
msgstr "Este código de cupón ya ha sido escaneado y activado."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "That promo code program has already been activated."
msgstr "Este programa de códigos promocionales ya se ha activado."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "That promo code program is expired."
msgstr "Este programa de códigos promocionales ha caducado."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "That promo code program is not yet valid."
msgstr "Este programa de códigos promocionales aún no es válido."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "That promo code program requires a specific pricelist."
msgstr ""
"Ese programa de código de promoción requiere una lista de precios "
"específica."

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__coupon_id
msgid "The coupon used to claim that reward."
msgstr "El cupón usado para obtener la recompensa."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid ""
"The following codes already exist in the database, perhaps they were already sold?\n"
"%s"
msgstr ""
"Los siguientes códigos ya existen en la base de datos,\n"
" ¿han sido ya vendidos?\n"
"1%s"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_mail__pos_report_print_id
msgid ""
"The report action to be executed when creating a coupon/gift card/loyalty "
"card in the PoS."
msgstr ""
"La acción de informe se ejecutará al crear un cupón, tarjeta regalo o "
"tarjeta de fidelidad en el TPV."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
#, python-format
msgid ""
"The reward \"%s\" contain an error in its domain, your domain must be "
"compatible with the PoS client"
msgstr ""
"La recompensa \"%s\" contiene un error en su dominio, el dominio debe ser "
"compatible con el cliente de TPV"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_id
msgid "The reward associated with this line."
msgstr "La recompensa relacionada con esta línea."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "The reward could not be applied."
msgstr "No se ha podido aplicar la recompensa."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid "There are not enough points for the coupon: %s."
msgstr "No hay suficientes puntos para el cupón: %s."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "There are not enough points on the coupon to claim this reward."
msgstr "No hay suficientes puntos en el cupón para reclamar esta recompensa."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "There are not enough products in the basket to claim this reward."
msgstr ""
"No tiene suficientes productos en la cesta para obtener esta recompensa."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"There is no email template on the gift card program and your pos is set to "
"print them."
msgstr ""
"No hay plantilla de correo electrónico en el programa de tarjetas regalo y "
"su TPV está configurado para enviarlas."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"There is no print report on the gift card program and your pos is set to "
"print them."
msgstr ""
"No hay informe de impresión en el programa de tarjetas de regalo y su TPV "
"está configurado para imprimirlas"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "These are the products that are valid for this rule."
msgstr "Estos son los productos válidos para esta regla."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is expired (%s)."
msgstr "Este cupón ha caducado (%s)."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is invalid (%s)."
msgstr "Este cupón no es válido (%s)."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is not available with the current pricelist."
msgstr "Este cupón no está disponible con la lista de precios actual. "

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is not yet valid (%s)."
msgstr "Este cupón aún no es válido (%s)."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
#, python-format
msgid "This gift card has already been sold"
msgstr "Esta tarjeta de regalo ya ha sido vendida."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid ""
"This gift card is not linked to any order. Do you really want to apply its "
"reward?"
msgstr ""
"Esta tarjeta de regalo no está vinculada a ningún pedido. \n"
"¿Realmente quiere aplicar la recompensa?"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "This is used to print the generated gift cards from PoS."
msgstr "Se utiliza para imprimir tarjetas de regalo desde el TPV."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"To continue, make the following reward products available in Point of Sale."
msgstr ""
"Para continuar, los siguientes productos de recompensa deben estar "
"disponibles en el TPV."

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_barcode_rule__type
msgid "Type"
msgstr "Tipo"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "Unknown discount type"
msgstr "Tipo de descuento desconocido"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "Unpaid gift card"
msgstr "Tarjeta de regalo impagada"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "Unpaid gift card rejected."
msgstr "Tarjeta de regalo impagada rechazada."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid "Use eWallet to pay"
msgstr "Usar el monedero electrónico para pagar"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "Valid Product"
msgstr "Producto válido"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#, python-format
msgid "Valid until:"
msgstr "Válido hasta:"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Whether this line is part of a reward or not."
msgstr "Si esta línea es parte de una recompensa o no."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#, python-format
msgid "Won:"
msgstr "Ganado:"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.js:0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/pos_store.js:0
#, python-format
msgid "You cannot sell a gift card that has already been sold."
msgstr "No puede vender una tarjeta de regalo que ya se vendió."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/product_screen/product_screen.js:0
#, python-format
msgid "You cannot set negative quantity or price to gift card or ewallet."
msgstr ""
"No puede establecer una cantidad o precio negativo en la tarjeta de regalo o"
" el monedero electrónico."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid ""
"You either have not created an eWallet or all your eWallets have expired."
msgstr ""
"No ha creado un monedero electrónico o todos sus monederos electrónicos han "
"caducado."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/loyalty_program.py:0
#, python-format
msgid "You must set '%s' before setting '%s'."
msgstr "Debe agregar '%s' antes de fijar '%s"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid "eWallet"
msgstr "Monedero electrónico"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid "eWallet Pay"
msgstr "Pagar con monedero electrónico"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/app/control_buttons/e_wallet_button/e_wallet_button.js:0
#, python-format
msgid "eWallet Refund"
msgstr "Reembolso de monedero electrónico"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "eWallet requires a customer to be selected"
msgstr "Debe seleccionar un cliente para el monedero electrónico"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/components/order_receipt/order_receipt.xml:0
#, python-format
msgid "no expiration"
msgstr "sin caducidad"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/overrides/models/loyalty.js:0
#, python-format
msgid "the gift cards"
msgstr "las tarjetas de regalo"
