<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="pos_coupon.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('pos-receipt')]//div[hasclass('before-footer')]" position="inside">
            <t t-foreach="props.data.loyaltyStats or []" t-as="_loyaltyStat" t-key="_loyaltyStat.couponId">
                <!-- Show only if portal_visible. -->
                <div t-if="_loyaltyStat.program.portal_visible and (_loyaltyStat.points.won || _loyaltyStat.points.spent)" class='loyalty'>
                    <span class="pos-receipt-center-align">
                        <div>--------------------------------</div>
                        <br/>
                        <div t-esc='_loyaltyStat.program.name' class="pos-receipt-title" />
                        <br />
                    </span>
                    <t t-if='_loyaltyStat.points.won'>
                        <div><t t-esc="_loyaltyStat.points.name"/> Won: <span t-esc='_loyaltyStat.points.won' class="pos-receipt-right-align"/></div>
                    </t>
                    <t t-if='_loyaltyStat.points.spent'>
                        <div><t t-esc="_loyaltyStat.points.name"/> Spent: <span t-esc='_loyaltyStat.points.spent' class="pos-receipt-right-align"/></div>
                    </t>
                    <!-- Don't use points.total, it's wrong in this context (after the order synced). -->
                    <!-- Show balance as it's updated during _postPushOrderResolve. -->
                    <t t-if='_loyaltyStat.points.balance'>
                        <div>Balance <t t-esc="_loyaltyStat.points.name"/>: <span t-esc='_loyaltyStat.points.balance' class="pos-receipt-right-align"/></div>
                    </t>
                    <br />
                </div>
            </t>
            <t t-if="props.data.partner">
                <br/>
                <div>Customer <span t-esc='props.data.partner.name' class='pos-receipt-right-align'/></div>
            </t>
            <t t-if="props.data.new_coupon_info and props.data.new_coupon_info.length !== 0">
                <div class="pos-coupon-rewards">
                    <div>------------------------</div>
                    <br/>
                    <div>
                        Coupon Codes
                    </div>
                    <t t-foreach="props.data.new_coupon_info" t-as="coupon_info" t-key="coupon_info.code">
                        <div class="coupon-container">
                            <div style="font-size: 110%;">
                                <t t-esc="coupon_info['program_name']"/>
                            </div>
                            <div>
                                <span>Valid until: </span> 
                                <t t-if="coupon_info['expiration_date']">
                                    <t t-esc="coupon_info['expiration_date']"/>
                                </t>
                                <t t-else="">
                                    no expiration
                                </t>
                            </div>
                            <div>
                                <img t-att-src="'/report/barcode/Code128/'+coupon_info['code']" style="width:200px;height:50px" alt="Barcode"/>
                            </div>
                            <div>
                                <t t-esc="coupon_info['code']"/>
                            </div>
                        </div>
                    </t>
                </div>
            </t>
        </xpath>
    </t>

</templates>
