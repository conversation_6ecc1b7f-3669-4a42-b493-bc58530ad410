<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pos_payment_method_view_form_inherit_pos_mercado_pago" model="ir.ui.view">
        <field name="name">pos.payment.method.form.inherit.mercado_pago</field>
        <field name="model">pos.payment.method</field>
        <field name="inherit_id" ref="point_of_sale.pos_payment_method_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='use_payment_terminal']" position="after">
                <!-- MercadoPago -->
                <field name="mp_bearer_token" placeholder="APP_USR-..." invisible="use_payment_terminal != 'mercado_pago'" required="use_payment_terminal == 'mercado_pago'"/>
                <field name="mp_webhook_secret_key" placeholder="c2f3662..." invisible="use_payment_terminal != 'mercado_pago'" required="use_payment_terminal == 'mercado_pago'"/>
                <field name="mp_id_point_smart" placeholder="1494126963" invisible="use_payment_terminal != 'mercado_pago'" required="use_payment_terminal == 'mercado_pago'"/>
                <button string="Force PDV" type="object" name="force_pdv" groups="base.group_no_one" class="oe_highlight" invisible="use_payment_terminal != 'mercado_pago'" required="use_payment_terminal == 'mercado_pago'"/>
            </xpath>
        </field>
    </record>
</odoo>
