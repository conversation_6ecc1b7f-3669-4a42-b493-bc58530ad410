# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_paytm
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__accept_payment
msgid "Accept Payment"
msgstr "Nõustu maksega"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__allowed_payment_modes__all
msgid "All"
msgstr "Kõik"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__allowed_payment_modes
msgid "Allowed Payment Modes"
msgstr "Lubatud makseviisid"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__accept_payment__auto
msgid "Automatically"
msgstr "Automaatselt"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__allowed_payment_modes__card
msgid "Card"
msgstr "Kaart"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__accept_payment__manual
msgid "Manually"
msgstr "Käsitsi"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__channel_id
msgid "PayTM Channel ID"
msgstr "PayTM kanali ID"

#. module: pos_paytm
#. odoo-javascript
#: code:addons/pos_paytm/static/src/js/payment_paytm.js:0
#, python-format
msgid "PayTM Error"
msgstr "PayTM veateade"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_merchant_key
msgid "PayTM Merchant API Key"
msgstr "PayTM kaupmehe API võti"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_mid
msgid "PayTM Merchant ID"
msgstr "PayTM kaupmehe ID"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_tid
msgid "PayTM Terminal ID"
msgstr "PayTM terminali ID"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment_method__paytm_test_mode
msgid "PayTM Test Mode"
msgstr "PayTM testrežiim"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment__paytm_authcode
msgid "Paytm APPR Code"
msgstr "PayTM APPR kood"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment__paytm_card_scheme
msgid "Paytm Card Scheme"
msgstr "PayTM kaardi skeem"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment__paytm_issuer_card_no
msgid "Paytm Issue Mask Card No."
msgstr ""

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment__paytm_issuer_bank
msgid "Paytm Issuer Bank"
msgstr "PayTM väljastav pank"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment__paytm_reference_no
msgid "Paytm Merchant Reference No."
msgstr "Paytm kaupmehe viitenumber"

#. module: pos_paytm
#: model:ir.model.fields,field_description:pos_paytm.field_pos_payment__paytm_payment_method
msgid "Paytm Payment Method"
msgstr "PayTM makseviis"

#. module: pos_paytm
#: model:ir.model,name:pos_paytm.model_pos_order
msgid "Point of Sale Orders"
msgstr "Kassa tellimused"

#. module: pos_paytm
#: model:ir.model,name:pos_paytm.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Kassa maksemeetodid"

#. module: pos_paytm
#: model:ir.model,name:pos_paytm.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Müügikoha maksed"

#. module: pos_paytm
#: model:ir.model.fields.selection,name:pos_paytm.selection__pos_payment_method__allowed_payment_modes__qr
msgid "QR"
msgstr "QR"

#. module: pos_paytm
#. odoo-javascript
#: code:addons/pos_paytm/static/src/js/payment_paytm.js:0
#, python-format
msgid "Reference number mismatched"
msgstr "Viitenumber ei sobitu"

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
#, python-format
msgid "Something went wrong with paytm request. Please try later."
msgstr ""

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
#, python-format
msgid "This Payment Terminal is only valid for INR Currency"
msgstr ""

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
#, python-format
msgid "makePaymentRequest expected resultCode not found in the response"
msgstr ""

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
#, python-format
msgid "paymentFetchRequest expected resultCode not found in the response"
msgstr ""

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
#, python-format
msgid "paytm transaction failure"
msgstr "PayTM tehingu ebaõnnestumine"

#. module: pos_paytm
#. odoo-python
#: code:addons/pos_paytm/models/pos_payment_method.py:0
#, python-format
msgid "paytm transaction request declined"
msgstr "Paytm tehingu taotlus on tagasi lükatud"
