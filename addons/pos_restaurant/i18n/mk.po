# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_restaurant
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2016-07-08 16:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/odoo/odoo-9/language/mk/)\n"
"Language: mk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  mk.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"
"#-#-#-#-#  mk.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sales: </strong>"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "ADD"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "ADD FLOOR"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "ADD a floor to start"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "ADD your first table"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Активно"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Add"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Add a tip"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.js:0
#, python-format
msgid "Add internal Note"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add internal notes on order lines for the kitchen"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment (North America specific)"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Adjust Amount"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Adjust the amount authorized by payment terminals to add a tip after the customers left or at the end of the day."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom Internal notes on Orderlines."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Amount"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Are you sure?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Back"
msgstr "Назад"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#, python-format
msgid "Back to floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Боја на позадина"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Слика на позадина"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/PrintBillButton.xml:0
#, python-format
msgid "Bill"
msgstr "Сметка"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "Печатење сметка"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Делење на сметки"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Blocked action"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Blue"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "CLOSE"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "COPY"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash Bar"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Close Tab"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Боја"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Copy"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/error_handlers.js:0
#, python-format
msgid "Couldn't synchronize the orders for the tables because you are offline"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Креирано од"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Креирано на"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "DELETE"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Delete"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Delete Error"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Прикажи име"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Edit Plan"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "FILL"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
#, python-format
msgid "Floor Name"
msgstr "Име на спрат"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Floor Name ?"
msgstr ""

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr ""

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Green"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Grey"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TableGuestsButton.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr "Гости"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Guests:"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Guests?"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Висина"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid "If false, the table is deactivated and will not be available in the point of sale"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.xml:0
#, python-format
msgid "Internal Note"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Internal Note added by the waiter."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Internal Notes"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Keep Open"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Последно ажурирање од"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Последно ажурирање на"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Light grey"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Logo"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "No Tip"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "No floors available,"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#, python-format
msgid "No tables available,"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Note"
msgstr "Белешка"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Number of Seats?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Ok"
msgstr "Во ред"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Open"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Orange"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#, python-format
msgid "Order"
msgstr "Нарачка"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "PRO FORMA"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#, python-format
msgid "Pay"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#, python-format
msgid "Payment"
msgstr "Плаќање"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_ids
msgid "Point of Sales"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_orderline_notes
msgid "Pos Iface Orderline Notes"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Print"
msgstr "Печати"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers due to no default printing protocol is available. It is possible to print your tickets by making use of an IoT Box."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Purple"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "RENAME"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Red"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Removing a floor cannot be undone. Do you still wanna remove %s?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Rename"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Reprint receipts"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Restaurant & Bar"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Спратови во ресторан"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Round Shape"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "SEATS"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "SHAPE"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Секвенца"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Услужен од"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Settle"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Signature"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SplitBillButton.xml:0
#, python-format
msgid "Split"
msgstr "Раздели"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Square Shape"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Subtotal"
msgstr "Вкупно"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Switch Floor View"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
#, python-format
msgid "Table"
msgstr "Табела"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "Име на табела"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "Table Name?"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "Тел:"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/error_handlers.js:0
#, python-format
msgid "The orders for the table could not be loaded because you are offline"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid "The table's color, expressed as a valid 'background' CSS property value"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid "The table's horizontal position from the left side to the table's center, in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid "The table's vertical position from the top to the table's center, in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "Ширината на табелата во пиксели"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "This order is not yet synced to server. Make sure it is synced then try again."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Tint"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#, python-format
msgid "Tip"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tip:"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Tipping"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Total:"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TransferOrderButton.xml:0
#, python-format
msgid "Transfer"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Turquoise"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "ДДВ:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "White"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Ширина"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "With a"
msgstr "Со"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/edit_bar.xml:0
#, python-format
msgid "Yellow"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "You cannot delete a floor when orders are still in draft for this floor."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#, python-format
msgid "You cannot delete a table with orders still in draft for this table."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "You cannot put a number that exceeds %s "
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "You cannot remove a floor that is used in a PoS session, close the session(s) first: \n"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "You cannot remove a table that is used in a PoS session, close the session(s) first."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "________________________"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "______________________________________________"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "at"
msgstr "на"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "at table"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "discount"
msgstr "попуст"
