# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_sale
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "(left:"
msgstr "(اليسار: "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "(tax incl.)"
msgstr "(شامل الضريبة) "

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
msgid "<span style=\"margin: 0px 5px;\">:</span>"
msgstr "<span style=\"margin: 0px 5px;\">:</span>"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "A new order has been created."
msgstr "تم إنشاء طلب جديد. "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Apply a down payment (fixed amount)"
msgstr "تطبيق دفعة مقدّمة (مبلغ ثابت) "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Apply a down payment (percentage)"
msgstr "تطبيق دفعة مقدّمة (نسبة) "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "Back"
msgstr "العودة"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Cancelled"
msgstr "تم الإلغاء "

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "سعر صرف العملة"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Customer"
msgstr "العميل"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Customer loading error"
msgstr "خطأ عند تحميل العميل "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.message_body
msgid "Delivered from"
msgstr "تم التوصيل من "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Do you want to load the SN/Lots linked to the Sales Order?"
msgstr ""
"هل ترغب في تحميل الأرقام التسلسلية/أرقام الدفعات المرتبطة بأمر البيع؟ "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Down Payment"
msgstr "الدفعة المقدّمة"

#. module: pos_sale
#: model:product.template,name:pos_sale.default_downpayment_product_product_template
msgid "Down Payment (POS)"
msgstr "دفعة مقدمة (نقطة البيع) "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__down_payment_details
msgid "Down Payment Details"
msgstr "تفاصيل الدفعة المقدمة "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__down_payment_product_id
#: model:ir.model.fields,field_description:pos_sale.field_res_config_settings__pos_down_payment_product_id
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Down Payment Product"
msgstr "منتج الدفعة المقدّمة"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Due balance: %s"
msgstr "الرصيد المستحق: %s "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "E.g. customer: Steward, date: 2020-05-09"
msgstr "على سبيل المثال، العميل: Steward، التاريخ: 2020-05-09 "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Error amount too high"
msgstr "خطأ، المبلغ أكبر من المفترض "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
#, python-format
msgid "From"
msgstr "من"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__invoiced
msgid "Invoiced"
msgstr "مفوتر"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid ""
"It seems that you didn't configure a down payment product in your point of "
"sale. You can go to your point of sale configuration to choose one."
msgstr ""
"يبدو أنك لم تقم بتهيئة منتج دفعة مقدمة في نقطة البيع الخاصة بك. يمكنك الذهاب"
" إلى تهيئة نقطة البيع لاختيار واحد. "

#. module: pos_sale
#. odoo-python
#: code:addons/pos_sale/models/sale_order.py:0
#, python-format
msgid "Linked POS Orders"
msgstr "طلبات نقطة البيع المرتبطة "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_origin_id
msgid "Linked Sale Order"
msgstr "أوامر البيع المرتبطة "

#. module: pos_sale
#. odoo-python
#: code:addons/pos_sale/models/pos_order.py:0
#, python-format
msgid "Linked Sale Orders"
msgstr "أوامر البيع المرتبطة "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Locked"
msgstr "مقفل"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "Next Order List"
msgstr "قائمة الطلبات التالية "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "No"
msgstr "لا"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "No down payment product"
msgstr "لا يوجد منتج دفعة مقدمة "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_sessions_open_count
msgid "Open POS Sessions"
msgstr "فتح جلسات نقطة البيع"

#. module: pos_sale
#: model:ir.actions.act_window,name:pos_sale.pos_session_action_from_crm_team
msgid "Open Sessions"
msgstr "الجلسات المفتوحة"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Order"
msgstr "الطلب"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_line_ids
#: model:ir.model.fields,field_description:pos_sale.field_sale_order_line__pos_order_line_ids
msgid "Order lines Transfered to Point of Sale"
msgstr "تم تحويل بنود الطلب إلى نقطة البيع "

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__order_reference__pos_order
msgid "POS Order"
msgstr "طلب نقطة البيع"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__paid
msgid "Paid"
msgstr "مدفوع"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_config
msgid "Point of Sale Configuration"
msgstr "تهيئة نقطة البيع "

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "بنود طلب نقطة البيع "

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order
msgid "Point of Sale Orders"
msgstr "طلبات نقطة البيع "

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_session
msgid "Point of Sale Session"
msgstr "جلسة نقطة البيع"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_config_ids
msgid "Point of Sales"
msgstr "نقاط البيع"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_count
msgid "Pos Order Count"
msgstr "عدد طلبات نقطة البيع "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "Previous Order List"
msgstr "قائمة الطلبات السابقة "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Products not available in POS"
msgstr "المنتجات غير متوفرة في نقطة البيع "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Quotation"
msgstr "عرض سعر"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Quotation Sent"
msgstr "تم إرسال عرض السعر"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/set_sale_order_button/set_sale_order_button.xml:0
#, python-format
msgid "Quotation/Order"
msgstr "عرض السعر/الطلب "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__order_reference
msgid "Related Order"
msgstr "الطلب ذو الصلة "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "SN/Lots Loading"
msgstr "تحميل الرقم التسلسلي/أرقام الدفعات "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/overrides/components/product_screen/product_screen.xml:0
#: code:addons/pos_sale/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "SO"
msgstr "أمر البيع "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__sale_order_count
msgid "Sale Order Count"
msgstr "عدد أوامر البيع"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales"
msgstr "المبيعات"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "تقرير المبيعات التحليلي"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#: model:ir.model,name:pos_sale.model_sale_order
#, python-format
msgid "Sales Order"
msgstr "أمر البيع"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_crm_team
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_session__crm_team_id
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "فريق المبيعات"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_res_config_settings__pos_crm_team_id
msgid "Sales Team (PoS)"
msgstr "فريق المبيعات (نقطة البيع) "

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales are reported to the following sales team"
msgstr "لم يتم إعداد تقارير بشأن المبيعات إلى فريق المبيعات التالي "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Salesman"
msgstr "مندوب المبيعات"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#, python-format
msgid "Salesperson"
msgstr "مندوب المبيعات "

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Session Running"
msgstr "الجلسة الجارية"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_order_amount_total
msgid "Session Sale Amount"
msgstr "قيمة مبيعات الجلسة"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Sessions Running"
msgstr "الجلسات الجارية"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/set_sale_order_button/set_sale_order_button.xml:0
#: code:addons/pos_sale/static/src/app/set_sale_order_button/set_sale_order_button.xml:0
#, python-format
msgid "Set Sale Order"
msgstr "تعيين أمر بيع "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Settle the order"
msgstr "تسوية الطلب "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid ""
"Some of the products in your Sale Order are not available in POS, do you "
"want to import them?"
msgstr ""
"بعض المنتجات المدرجة في أمر مبيعاتك غير متوفرة في نقطة البيع. هل ترغب في "
"استيرادها؟ "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_line_id
msgid "Source Sale Order Line"
msgstr "بند أمر البيع المصدري "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "State"
msgstr "الولاية "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__state
msgid "Status"
msgstr "الحالة"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_sale_order__amount_unpaid
msgid "The amount due from the sale order."
msgstr "المبلغ المستحق من أمر البيع. "

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "نسبة العملة مقابل العملة ذات النسبة الممكن تطبيقها في تاريخ الطلب "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "There was a problem in loading the %s customer."
msgstr "حدث خطأ أثناء تحميل العميل %s. "

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_pos_session__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_res_config_settings__pos_crm_team_id
msgid "This Point of sale's sales will be related to this Sales Team."
msgstr "ستكون مبيعات نقطة البيع هذه مرتبطة بفريق المبيعات هذا."

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "This product will be applied when down payment is made"
msgstr "سوف يتم تطبيق هذا المنتج عندا تتم تسوية الدفعة المقدمة "

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__down_payment_product_id
#: model:ir.model.fields,help:pos_sale.field_res_config_settings__pos_down_payment_product_id
msgid "This product will be used as down payment on a sale order."
msgstr "سوف يُستخدم هذا المنتج كدفعة مقدمة في أمر البيع. "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Total"
msgstr "الإجمالي"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_stock_picking
msgid "Transfer"
msgstr "تحويل "

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_pos_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                from Sale"
msgstr ""
"تم التحويل<br/>\n"
"                                من البيع "

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                to POS"
msgstr ""
"تم التحويل<br/>\n"
"                                إلى نقطة البيع "

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__amount_unpaid
msgid "Unpaid Amount"
msgstr "المبلغ غير المدفوع "

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "What do you want to do?"
msgstr "ما الذي تريد فعله؟"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Yes"
msgstr "نعم"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid ""
"You have tried to charge a down payment of %s but only %s remains to be "
"paid, %s will be applied to the purchase order line."
msgstr ""
"لقد حاولت فرض دفعة أولى لـ %s ولكن المبلغ المتبقي هو %s فقط، سيتم تطبيق %s "
"لبند أمر الشراء. "
