<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="product_packaging_tree_view" model="ir.ui.view">
        <field name="name">product.packaging.tree.view</field>
        <field name="model">product.packaging</field>
        <field name="arch" type="xml">
            <tree string="Product Packagings" name="packaging">
                <field name="sequence" widget="handle"/>
                <field name="product_id"/>
                <field name="name" string="Packaging"/>
                <field name="qty"/>
                <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>
                <field name="barcode" optional="hide"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
                <field name="company_id" groups="!base.group_multi_company" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="product_packaging_tree_view2" model="ir.ui.view">
        <field name="name">product.packaging.tree.view2</field>
        <field name="model">product.packaging</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="product.product_packaging_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="replace"/>
            <xpath expr="//tree[@name='packaging']" position="attributes">
                <attribute name="editable">bottom</attribute>
            </xpath>
        </field>
    </record>

    <record id="product_packaging_form_view" model="ir.ui.view">
        <field name="name">product.packaging.form.view</field>
        <field name="model">product.packaging</field>
        <field name="arch" type="xml">
            <form string="Product Packaging">
                <sheet>
                    <label for="name" string="Packaging"/>
                    <h1>
                        <field name="name"/>
                    </h1>
                    <group>
                        <field name="id" invisible='1'/>
                        <field name="company_id" invisible="1"/>
                        <group name="group_product">
                            <field name="product_id" readonly="id"/>
                        </group>
                        <group name="qty">
                            <label for="qty" string="Contained quantity"/>
                            <div class="o_row">
                                <field name="qty"/>
                                <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>
                            </div>
                            <field name="barcode"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="product_packaging_form_view2" model="ir.ui.view">
        <field name="name">product.packaging.form.view2</field>
        <field name="model">product.packaging</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="product.product_packaging_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='group_product']" position="replace"/>
            <xpath expr="//field[@name='id']" position="replace"/>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_packaging_view">
        <field name="name">Product Packagings</field>
        <field name="res_model">product.packaging</field>
        <field name="domain">[]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('product_packaging_tree_view')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('product_packaging_form_view')})]"/>
    </record>

</odoo>
