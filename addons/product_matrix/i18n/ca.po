# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_matrix
# 
# Translators:
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <car<PERSON><EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON> Bo<PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON> i Bochaca <<EMAIL>>, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"<small>Switch to the \"else\" condition of this block to view or edit the "
"table.</small>"
msgstr ""

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "<strong>Product matrix block</strong>"
msgstr ""

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_1
msgid "Blue"
msgstr "Blau"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Cell name"
msgstr ""

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/js/product_matrix_dialog.js:0
#, python-format
msgid "Choose Product Variants"
msgstr "Escolliu les variants del producte"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Column name"
msgstr ""

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_gender
msgid "Gender"
msgstr "Sexe"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_m
msgid "Men"
msgstr "Home"

#. module: product_matrix
#: model:product.template,name:product_matrix.matrix_product_template_shirt
msgid "My Company Tshirt (GRID)"
msgstr "Samarreta de la meva empresa (GRID)"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix.xml:0
#, python-format
msgid "Not available"
msgstr "No disponible"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_2
msgid "Pink"
msgstr "Rosa"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template
msgid "Product"
msgstr "Producte"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Valor d' atribut de plantilla de producte"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_4
msgid "Rainbow"
msgstr "Arc de Sant Martí"

#. module: product_matrix
#: model:product.template,description_sale:product_matrix.matrix_product_template_shirt
msgid "Show your company love around you =)."
msgstr "Mostra l'amor de la teva empresa al teu voltant =)."

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_size
msgid "Size"
msgstr "Mida"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"The matrix of product variants of this order will be displayed here, if "
"there are any."
msgstr ""

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.extra_price
msgid "Variant price"
msgstr ""

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_w
msgid "Women"
msgstr "Dones"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xl
msgid "XL"
msgstr "XL"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xs
msgid "XS"
msgstr "XS"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_3
msgid "Yellow"
msgstr "Groc"
