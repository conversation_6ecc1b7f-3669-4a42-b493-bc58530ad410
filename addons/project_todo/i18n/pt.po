# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_todo
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"&amp;#128075; <br/>\n"
"    Welcome to the To-do app!"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font class=\"bg-o-color-2\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        During the conversion of a to-do to a task, you will be able to add or remove assignees to control who has access to the created task. Note that its visibility will also depend on the visibility settings of the project you are assigning it to.\n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        To-dos are now like \n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">private tasks</font>\n"
"        </span>\n"
"        and will appear in the Project app under \n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">My Tasks</font>.\n"
"        </span>\n"
"        Similarly, private tasks created in the Project app will also appear in your to-dos.\n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        To-dos, like private tasks, are only accessible to the users specified as assignees. You can use the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">assignees</font>\n"
"        </span>\n"
"        field above to share this to-do with other users.\n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Use it to manage your urgent work, take notes on the go, and create tasks based on them. \n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        You can convert your to-dos into tasks and assign them to a project. To do this, you need to access the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Convert to Task</font>\n"
"        </span>\n"
"        pop-up from the action menu of the to-do.\n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "<span style=\"font-size: 14px;\">Press Ctrl-Z to undo any change</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private to-do is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following</span>"
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_type__category
msgid "Action"
msgstr "Ação"

#. module: project_todo
#: model:ir.model.fields,help:project_todo.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"As ações podem despoletar comportamentos específicos, como abrir uma "
"visualização do calendário ou marcar automaticamente como concluído quando é"
" enviado um documento"

#. module: project_todo
#: model:ir.model,name:project_todo.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipo de Atividade"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add To-Do"
msgstr ""

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/web/activity/activity_menu_patch.js:0
#: code:addons/project_todo/static/src/web/activity/activity_menu_patch.js:0
#, python-format
msgid "Add a To-Do"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add details about your to-do..."
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Archived"
msgstr "Arquivados"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__user_id
msgid "Assigned to"
msgstr "Atribuído a"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Assignees"
msgstr "Responsáveis"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By assigned tags"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By personal stages"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Choose tags from the selected project"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed"
msgstr "Encerrado"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed On"
msgstr "Encerrado A"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_controller.js:0
#: model:ir.actions.act_window,name:project_todo.project_task_action_convert_todo_to_task
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
#, python-format
msgid "Convert to Task"
msgstr "Converter em Tarefa"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Create a task"
msgstr ""

#. module: project_todo
#: model:ir.model,name:project_todo.model_mail_activity_todo_create
msgid "Create activity and todo at the same time"
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_date
msgid "Created on"
msgstr "Criado em"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Discard"
msgstr "Descartar"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__display_name
msgid "Display Name"
msgstr "Nome"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__date_deadline
msgid "Due Date"
msgstr "Data de Vencimento"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Future Activities"
msgstr "Atividades Futuras"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Group By"
msgstr "Agrupar por"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Hey"
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__id
msgid "ID"
msgstr "ID"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid ""
"Keep your work organized by using memos and to-do lists.\n"
"                Your to-do items are private by default, but you can choose to share them with others by adding them as assignees."
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Late Activities"
msgstr "Atividades em Atraso"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/components/todo_done_checkmark/todo_done_checkmark.xml:0
#: code:addons/project_todo/static/src/components/todo_done_checkmark/todo_done_checkmark.xml:0
#, python-format
msgid "Mark as done"
msgstr "Marcar como feito"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid "No to-do found. Let's create one!"
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__note
msgid "Note"
msgstr "Nota"

#. module: project_todo
#: model:res.groups,name:project_todo.group_onboarding_todo
msgid "Onboarding todo already generated for those users"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Open"
msgstr "Aberto"

#. module: project_todo
#: model:ir.model.fields.selection,name:project_todo.selection__mail_activity_type__category__reminder
#: model:mail.activity.type,name:project_todo.mail_activity_data_reminder
msgid "Reminder"
msgstr "Lembrete"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Reminder to..."
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Select an existing project"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Show all records which has next action date is before today"
msgstr "Mostrar todos os registos cuja data de ação é anterior à atual"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "Stage"
msgstr "Etapa"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__summary
msgid "Summary"
msgstr "Sumário"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Tags"
msgstr "Etiquetas"

#. module: project_todo
#: model:ir.model,name:project_todo.model_project_task
msgid "Task"
msgstr "Tarefa"

#. module: project_todo
#: model:ir.ui.menu,name:project_todo.menu_todo_todos
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "To-do Title"
msgstr ""

#. module: project_todo
#: model:ir.actions.act_window,name:project_todo.project_task_action_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_activity
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "To-dos"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Today Activities"
msgstr "Atividades do Dia"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Todos"
msgstr ""

#. module: project_todo
#. odoo-javascript
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
#: code:addons/project_todo/static/src/components/todo_editable_breadcrumb_name/todo_editable_breadcrumb_name.js:0
#, python-format
msgid "Untitled to-do"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Using the editor"
msgstr ""

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
#, python-format
msgid "Welcome %s!"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Who has access to what?"
msgstr ""

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/wizard/mail_activity_todo_create.py:0
#, python-format
msgid "Your to-do has been successfully added to your pipeline."
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "e.g. Send Invitations"
msgstr ""

#. module: project_todo
#: model:ir.actions.server,name:project_todo.project_task_preload_action_todo
msgid "menu load To-dos"
msgstr ""
