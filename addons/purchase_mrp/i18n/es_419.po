# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_mrp
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.purchase_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Fabricación</span>"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.mrp_production_form_view_purchase
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Compras</span>"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Reporte de resumen de la lista de materiales"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr "Lista de materiales"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Línea de lista de materiales"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
#, python-format
msgid "Components cost share have to be positive or equals to zero."
msgstr ""
"El costo compartido de los componentes tiene que ser positivo o igual a "
"cero."

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_bom_line__cost_share
msgid "Cost Share (%)"
msgstr "Reparto de costos (%)"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_purchase_order__mrp_production_count
msgid "Count of MO Source"
msgstr "Número de órdenes de fabricación fuente"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_production__purchase_order_count
msgid "Count of generated PO"
msgstr "Número de órdenes de compra generadas"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Reporte del resumen de la orden de fabricación"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/purchase.py:0
#, python-format
msgid "Manufacturing Source of %s"
msgstr "Fuente de fabricación de %s"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/stock_move.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo no puede generar los asientos anglosajones. La valuación total de %s es"
" cero."

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_production
msgid "Production Order"
msgstr "Orden de producción"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order
msgid "Purchase Order"
msgstr "Orden de compra"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Línea de la orden de compra"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_production.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr "Orden de compra generada a partir de %s"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de stock"

#. module: purchase_mrp
#: model:ir.model.fields,help:purchase_mrp.field_mrp_bom_line__cost_share
msgid ""
"The percentage of the component repartition cost when purchasing a kit.The "
"total of all components' cost have to be equal to 100."
msgstr ""
"El porcentaje del costo de repartición del componente cuando se compra un "
"kit. El total del costo de todos los componentes debe ser igual a 100."

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
#, python-format
msgid "The total cost share for a BoM's component have to be 100"
msgstr ""
"El total del reparto de costos para un componente de la lista de materiales "
"debe ser 100"
