# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_product_matrix
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "ค่าคุณลักษณะ"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Edit Configuration"
msgstr "แก้ไขการกำหนดค่า"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid
msgid "Grid"
msgstr "ตาราง"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "เทมเพลตตารางสินค้า"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_update
msgid "Grid Update"
msgstr "อัปเดตตาราง"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__report_grids
msgid ""
"If set, the matrix of configurable products will be shown on the report of "
"this order."
msgstr ""
"หากตั้งค่าไว้ "
"เมทริกซ์ของสินค้าที่สามารถกำหนดค่าได้จะแสดงในรายงานของคำสั่งซื้อนี้"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "สินค้าสามารถกำหนดค่าได้หรือไม่?"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__report_grids
msgid "Print Variant Grids"
msgstr "พิมพ์ตารางความแตกต่าง"

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product"
msgstr "สินค้า"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_id
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "ค่าคุณลักษณะของสินค้าที่ไม่สร้างรายละเอียดของตัวเลือกสินค้า"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order
msgid "Purchase Order"
msgstr "คำสั่งซื้อ"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "รายการคำสั่งซื้อ"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "ข้อมูลเทคนิคสำหรับฟังก์ชันการทำงานของ product_matrix"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid
msgid ""
"Technical storage of grid. \n"
"If grid_update, will be loaded on the PO. \n"
"If not, represents the matrix to open."
msgstr ""
"การจัดเก็บทางเทคนิคของตารางกริด\n"
"หาก grid_update จะถูกโหลดบน PO\n"
"ไม่เช่นนั้น จะแสดงเมทริกซ์ที่จะเปิด"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr "ช่องตารางมีเมทริกซ์ใหม่ที่จะใช้หรือไม่"

#. module: purchase_product_matrix
#. odoo-python
#: code:addons/purchase_product_matrix/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple purchase "
"lines."
msgstr ""
"คุณไม่สามารถเปลี่ยนแปลงปริมาณของสินค้าที่มีอยู่ในรายการซื้อหลายรายการได้"
