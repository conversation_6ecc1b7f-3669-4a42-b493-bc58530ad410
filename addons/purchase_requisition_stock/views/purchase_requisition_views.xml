<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_purchase_requisition_form_inherit" model="ir.ui.view">
            <field name="name">purchase.requisition.form.inherit</field>
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_form" />
            <field name="arch" type="xml">
                <field name="origin" position="after">
                    <field name="picking_type_id" options="{'no_open': True, 'no_create': True}" groups="stock.group_adv_location" readonly="state != 'draft'"/>
                </field>
            </field>
        </record>
    </data>
</odoo>
