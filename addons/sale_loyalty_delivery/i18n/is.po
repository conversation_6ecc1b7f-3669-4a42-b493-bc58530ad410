# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_loyalty_delivery
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Icelandic (https://app.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_reward.py:0
#, python-format
msgid " (Max %s)"
msgstr ""

#. module: sale_loyalty_delivery
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
msgid "( Max"
msgstr ""

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_program.py:0
#, python-format
msgid "Automatic promotion: free shipping on orders higher than $50"
msgstr ""

#. module: sale_loyalty_delivery
#: model:ir.model.fields.selection,name:sale_loyalty_delivery.selection__loyalty_reward__reward_type__shipping
msgid "Free Shipping"
msgstr ""

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/sale_order.py:0
#, python-format
msgid "Free Shipping - %s"
msgstr ""

#. module: sale_loyalty_delivery
#. odoo-python
#: code:addons/sale_loyalty_delivery/models/loyalty_reward.py:0
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_form_inherit_loyalty_delivery
#: model_terms:ir.ui.view,arch_db:sale_loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
#, python-format
msgid "Free shipping"
msgstr ""

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_loyalty_program
msgid "Loyalty Program"
msgstr ""

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_loyalty_reward
msgid "Loyalty Reward"
msgstr ""

#. module: sale_loyalty_delivery
#: model:ir.model.fields,field_description:sale_loyalty_delivery.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr ""

#. module: sale_loyalty_delivery
#: model:ir.model,name:sale_loyalty_delivery.model_sale_order
msgid "Sales Order"
msgstr "Sölupöntun"
