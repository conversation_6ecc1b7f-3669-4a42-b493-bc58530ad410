# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_configurator
# 
# Translators:
# Wil Odoo, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product/product.xml:0
#, python-format
msgid "Add"
msgstr "添加"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product/product.xml:0
#, python-format
msgid "Add one"
msgstr "添加一行"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_list/product_list.xml:0
#, python-format
msgid "Add optional products"
msgstr "添加可选产品"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "属性值"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: sale_product_configurator
#: model:product.template,name:sale_product_configurator.product_product_1_product_template
msgid "Chair floor protection"
msgstr "椅子地板保护"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_configurator_dialog/product_configurator_dialog.js:0
#, python-format
msgid "Configure your product"
msgstr "配置您的产品"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
#, python-format
msgid "Confirm"
msgstr "确认"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_template_attribute_line/product_template_attribute_line.xml:0
#, python-format
msgid "Enter a customized value"
msgstr "输入自定义值"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "产品是否可配置？"

#. module: sale_product_configurator
#: model:product.template,description_sale:sale_product_configurator.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr "办公椅会伤害您的地板：保护它。"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "可选产品"

#. module: sale_product_configurator
#: model:ir.model.fields,help:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale_product_configurator.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr "每当客户点击 * 加入购物车 *，就会出现建议的可选产品（交叉销售策略，例如，就电脑而言：保修、配套软件等）。"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_list/product_list.xml:0
#, python-format
msgid "Price"
msgstr "价格"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_list/product_list.xml:0
#: model:ir.model,name:sale_product_configurator.model_product_template
#, python-format
msgid "Product"
msgstr "产品"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product/product.xml:0
#: code:addons/sale_product_configurator/static/src/js/product/product.xml:0
#, python-format
msgid "Product Image"
msgstr "产品图像"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_order_view_form
msgid "Product Variant"
msgstr "产品变体"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_list/product_list.xml:0
#, python-format
msgid "Quantity"
msgstr "数量"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr "在“添加到购物车”或报价时推荐"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product/product.xml:0
#, python-format
msgid "Remove one"
msgstr "移除一行"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product/product.xml:0
#, python-format
msgid "Remove product"
msgstr "删除产品"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单行"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product/product.xml:0
#, python-format
msgid "This option or combination of options is not available"
msgstr "此选项或选项组合不可用"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/product_list/product_list.xml:0
#, python-format
msgid "Total:"
msgstr "总计："
