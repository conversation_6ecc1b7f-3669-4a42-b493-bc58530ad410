<?xml version="1.0" encoding="utf-8"?>
<odoo><data>

    <record id="crm_team_member_view_search" model="ir.ui.view">
        <field name="name">crm.team.member.view.search</field>
        <field name="model">crm.team.member</field>
        <field name="arch" type="xml">
            <search string="Sales Person">
                <field name="user_id"/>
                <field name="crm_team_id"/>
                <separator/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Sales Team" name="groupby_crm_team_id" context="{'group_by': 'crm_team_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="crm_team_member_view_tree" model="ir.ui.view">
        <field name="name">crm.team.member.view.tree</field>
        <field name="model">crm.team.member</field>
        <field name="arch" type="xml">
            <tree string="Sales Men" sample="1">
                <field name="crm_team_id"/>
                <field name="user_id"/>
            </tree>
        </field>
    </record>

    <record id="crm_team_member_view_tree_from_team" model="ir.ui.view">
        <field name="name">crm.team.member.view.tree.from.team</field>
        <field name="model">crm.team.member</field>
        <field name="inherit_id" ref="sales_team.crm_team_member_view_tree"/>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='crm_team_id']" position="replace">
            </xpath>
        </field>
    </record>

    <record id="crm_team_member_view_kanban" model="ir.ui.view">
        <field name="name">crm.team.member.view.kanban</field>
        <field name="model">crm.team.member</field>
        <field name="arch" type="xml">
            <kanban quick_create="false"
                group_create="0"
                records_draggable="0"
                sample="1"
                default_group_by="crm_team_id"
                class="o_crm_team_member_kanban">
                <field name="user_id"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="ribbon ribbon-top-right" invisible="active">
                                <span class="text-bg-danger">Archived</span>
                            </div>
                            <div class="o_kanban_card_content d-flex">
                                <div>
                                    <img t-att-src="kanban_image('res.users', 'avatar_128', record.user_id.raw_value)" class="o_kanban_image o_image_64_cover" alt="Avatar"/>
                                </div>
                                <div class="oe_kanban_details d-flex flex-column ms-3">
                                    <strong class="o_kanban_record_title oe_partner_heading"><field name="user_id"/></strong>
                                    <a type="open" class="nav-link p-0"><field name="crm_team_id"/></a>
                                    <div class="d-flex align-items-baseline text-break">
                                        <i class="fa fa-envelope me-1" role="img" aria-label="Email" title="Email"/><field name="email"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="crm_team_member_view_kanban_from_team" model="ir.ui.view">
        <field name="name">crm.team.member.view.kanban.from.team</field>
        <field name="model">crm.team.member</field>
        <field name="inherit_id" ref="sales_team.crm_team_member_view_kanban"/>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='crm_team_id']" position="replace">
            </xpath>
        </field>
    </record>

    <record id="crm_team_member_view_form" model="ir.ui.view">
        <field name="name">crm.team.member.view.form</field>
        <field name="model">crm.team.member</field>
        <field name="arch" type="xml">
            <form string="Sales Men">
                <field name="active" invisible="1"/>
                <field name="company_id" invisible="1"/>
                <field name="is_membership_multi" invisible="1"/>
                <field name="member_warning" invisible="1"/>
                <field name="user_in_teams_ids" invisible="1"/>
                <field name="user_company_ids" invisible="1"/>
                <sheet>
                    <div class="alert alert-info text-center" role="alert"
                        invisible="not member_warning">
                        <field name="member_warning"/>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <field name="image_1920" widget='image' class="oe_avatar"
                        invisible="not user_id"
                        options='{"preview_image": "image_128"}'/>
                    <div class="oe_title">
                        <label for="user_id" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="user_id" class="flex-grow-1"
                                options="{'no_quick_create': True}"/>
                        </h1>
                    </div>
                    <group name="member_partner_info">
                        <group name="group_owner">
                            <field name="crm_team_id" context="{'kanban_view_ref': 'sales_team.crm_team_view_kanban'}"/>
                        </group>
                        <group class="ps-0" name="group_user">
                            <field name="company_id" invisible="not user_id"
                                groups="base.group_multi_company"/>
                            <field name="email" invisible="not user_id"/>
                            <field name="mobile" invisible="not user_id"/>
                            <field name="phone" invisible="not user_id"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="crm_team_member_view_form_from_team" model="ir.ui.view">
        <field name="name">crm.team.member.view.form.from.team</field>
        <field name="model">crm.team.member</field>
        <field name="inherit_id" ref="sales_team.crm_team_member_view_form"/>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='group_owner']" position="replace"/>
        </field>
    </record>

    <record id="crm_team_member_action" model="ir.actions.act_window">
        <field name="name">Team Members</field>
        <field name="res_model">crm.team.member</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new salesman
            </p><p>
                Link salespersons to sales teams.
            </p>
        </field>
    </record>

</data></odoo>
