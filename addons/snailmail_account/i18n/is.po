# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Icelandic (https://app.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_move_send_inherit_snailmail
msgid ""
"<i class=\"fa fa-question-circle\" role=\"img\" aria-label=\"Warning\" "
"title=\"The address is unknown on the partner\" invisible=\"not "
"send_by_post_readonly\"/>"
msgstr ""

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_account_move_send
msgid "Account Move Send"
msgstr "Reikningur Færa Senda"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_move_send__checkbox_send_by_post
msgid "By Post"
msgstr ""

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_company
msgid "Companies"
msgstr "Fyrirtæki"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_config_settings
msgid "Config Settings"
msgstr "Stillingarvalkostir"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_move_send__enable_send_by_post
msgid "Enable Send By Post"
msgstr ""

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_account_move
msgid "Journal Entry"
msgstr "Dagbókarfærsla"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_move_send__send_by_post_readonly
msgid "Send By Post Readonly"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_move_send__send_by_post_warning_message
msgid "Send By Post Warning Message"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_res_company__invoice_is_snailmail
#: model:ir.model.fields,field_description:snailmail_account.field_res_config_settings__invoice_is_snailmail
msgid "Send by Post"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_move_send__send_by_post_cost
msgid "Stamps"
msgstr ""

#. module: snailmail_account
#. odoo-python
#: code:addons/snailmail_account/wizard/account_move_send.py:0
#, python-format
msgid ""
"The partners on the following invoices have no valid address, so those "
"invoices will not be sent: %s"
msgstr ""
