{"version": 12, "sheets": [{"id": "91067711-0fdf-451a-b1ec-9e8bdcff23f0", "name": "Dashboard", "colNumber": 7, "rowNumber": 44, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}, "31": {"size": 40}, "32": {"size": 40}}, "cols": {"0": {"size": 225}, "1": {"size": 150}, "2": {"size": 100}, "3": {"size": 50}, "4": {"size": 225}, "5": {"size": 150}, "6": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Expenses Analysis](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[],\"context\":{\"group_by\":[\"date:month\",\"product_id\"],\"graph_measure\":\"total_amount\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"date:month\",\"product_id\"]},\"modelName\":\"hr.expense\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Expenses Analysis\",\"positional\":true})", "border": 1}, "A19": {"style": 1, "content": "[Top Expenses](odoo://view/{\"viewType\":\"list\",\"action\":{\"domain\":\"[]\",\"context\":{\"group_by\":[]},\"modelName\":\"hr.expense\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"kanban\"],[false,\"form\"]]},\"threshold\":0,\"name\":\"Expenses Analysis\"})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Expense\")", "border": 2}, "A21": {"style": 3, "content": "=ODOO.LIST(1,1,\"name\")"}, "A22": {"style": 4, "content": "=ODOO.LIST(1,2,\"name\")"}, "A23": {"style": 3, "content": "=ODOO.LIST(1,3,\"name\")"}, "A24": {"style": 4, "content": "=ODOO.LIST(1,4,\"name\")"}, "A25": {"style": 3, "content": "=ODOO.LIST(1,5,\"name\")"}, "A26": {"style": 4, "content": "=ODOO.LIST(1,6,\"name\")"}, "A27": {"style": 3, "content": "=ODOO.LIST(1,7,\"name\")"}, "A28": {"style": 4, "content": "=ODOO.LIST(1,8,\"name\")"}, "A29": {"style": 3, "content": "=ODOO.LIST(1,9,\"name\")"}, "A30": {"style": 4, "content": "=ODOO.LIST(1,10,\"name\")"}, "A32": {"style": 1, "content": "[Top Reinvoiced Orders](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"sale_order_id\",\"!=\",false]],\"context\":{\"group_by\":[\"sale_order_id\"],\"pivot_measures\":[\"__count\",\"total_amount\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"sale_order_id\"]},\"modelName\":\"hr.expense\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Expenses Analysis\"})", "border": 1}, "A33": {"style": 2, "content": "=_t(\"Order\")", "border": 2}, "A34": {"style": 5, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",1)"}, "A35": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",2)"}, "A36": {"style": 5, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",3)"}, "A37": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",4)"}, "A38": {"style": 5, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",5)"}, "A39": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",6)"}, "A40": {"style": 5, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",7)"}, "A41": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",8)"}, "A42": {"style": 5, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",9)"}, "A43": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#sale_order_id\",10)"}, "B20": {"style": 2, "content": "=_t(\"Employee\")", "border": 2}, "B21": {"style": 7, "content": "=ODOO.LIST(1,1,\"employee_id\")"}, "B22": {"content": "=ODOO.LIST(1,2,\"employee_id\")"}, "B23": {"style": 7, "content": "=ODOO.LIST(1,3,\"employee_id\")"}, "B24": {"content": "=ODOO.LIST(1,4,\"employee_id\")"}, "B25": {"style": 7, "content": "=ODOO.LIST(1,5,\"employee_id\")"}, "B26": {"content": "=ODOO.LIST(1,6,\"employee_id\")"}, "B27": {"style": 7, "content": "=ODOO.LIST(1,7,\"employee_id\")"}, "B28": {"content": "=ODOO.LIST(1,8,\"employee_id\")"}, "B29": {"style": 7, "content": "=ODOO.LIST(1,9,\"employee_id\")"}, "B30": {"content": "=ODOO.LIST(1,10,\"employee_id\")"}, "B33": {"style": 8, "content": "=_t(\"# Expenses\")", "border": 2}, "B34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",1)"}, "B35": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",2)"}, "B36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",3)"}, "B37": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",4)"}, "B38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",5)"}, "B39": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",6)"}, "B40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",7)"}, "B41": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",8)"}, "B42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",9)"}, "B43": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#sale_order_id\",10)"}, "C20": {"style": 8, "content": "=_t(\"Total\")", "border": 2}, "C21": {"style": 7, "content": "=ODOO.LIST(1,1,\"total_amount\")"}, "C22": {"content": "=ODOO.LIST(1,2,\"total_amount\")"}, "C23": {"style": 7, "content": "=ODOO.LIST(1,3,\"total_amount\")"}, "C24": {"content": "=ODOO.LIST(1,4,\"total_amount\")"}, "C25": {"style": 7, "content": "=ODOO.LIST(1,5,\"total_amount\")"}, "C26": {"content": "=ODOO.LIST(1,6,\"total_amount\")"}, "C27": {"style": 7, "content": "=ODOO.LIST(1,7,\"total_amount\")"}, "C28": {"content": "=ODOO.LIST(1,8,\"total_amount\")"}, "C29": {"style": 7, "content": "=ODOO.LIST(1,9,\"total_amount\")"}, "C30": {"content": "=ODOO.LIST(1,10,\"total_amount\")"}, "C33": {"style": 8, "content": "=_t(\"Total\")", "border": 2}, "C34": {"style": 7, "content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",1)"}, "C35": {"content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",2)"}, "C36": {"style": 7, "content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",3)"}, "C37": {"content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",4)"}, "C38": {"style": 7, "content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",5)"}, "C39": {"content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",6)"}, "C40": {"style": 7, "content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",7)"}, "C41": {"content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",8)"}, "C42": {"style": 7, "content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",9)"}, "C43": {"content": "=ODOO.PIVOT(9,\"total_amount\",\"#sale_order_id\",10)"}, "E19": {"style": 1, "content": "[Top Categories](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"product_id\",\"!=\",false]],\"context\":{\"group_by\":[\"product_id\"],\"pivot_measures\":[\"__count\",\"total_amount\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_id\"]},\"modelName\":\"hr.expense\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Expenses Analysis\"})", "border": 1}, "E20": {"style": 2, "content": "=_t(\"Category\")", "border": 2}, "E21": {"style": 5, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",1)"}, "E22": {"style": 6, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",2)"}, "E23": {"style": 5, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",3)"}, "E24": {"style": 6, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",4)"}, "E25": {"style": 5, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",5)"}, "E26": {"style": 6, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",6)"}, "E27": {"style": 5, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",7)"}, "E28": {"style": 6, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",8)"}, "E29": {"style": 5, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",9)"}, "E30": {"style": 6, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",10)"}, "E32": {"style": 1, "content": "[Top Employees](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"product_id\",\"!=\",false]],\"context\":{\"group_by\":[\"product_id\"],\"pivot_measures\":[\"__count\",\"total_amount\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_id\"]},\"modelName\":\"hr.expense\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Expenses Analysis\"})", "border": 1}, "E33": {"style": 2, "content": "=_t(\"Employee\")", "border": 2}, "E34": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",1)"}, "E35": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",2)"}, "E36": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",3)"}, "E37": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",4)"}, "E38": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",5)"}, "E39": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",6)"}, "E40": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",7)"}, "E41": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",8)"}, "E42": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",9)"}, "E43": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#employee_id\",10)"}, "F20": {"style": 8, "content": "=_t(\"# Expenses\")", "border": 2}, "F21": {"style": 7, "content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",1)"}, "F22": {"content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",2)"}, "F23": {"style": 7, "content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",3)"}, "F24": {"content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",4)"}, "F25": {"style": 7, "content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",5)"}, "F26": {"content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",6)"}, "F27": {"style": 7, "content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",7)"}, "F28": {"content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",8)"}, "F29": {"style": 7, "content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",9)"}, "F30": {"content": "=ODOO.PIVOT(1,\"__count\",\"#product_id\",10)"}, "F33": {"style": 8, "content": "=_t(\"# Expenses\")", "border": 2}, "F34": {"style": 7, "content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",1)"}, "F35": {"content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",2)"}, "F36": {"style": 7, "content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",3)"}, "F37": {"content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",4)"}, "F38": {"style": 7, "content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",5)"}, "F39": {"content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",6)"}, "F40": {"style": 7, "content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",7)"}, "F41": {"content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",8)"}, "F42": {"style": 7, "content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",9)"}, "F43": {"content": "=ODOO.PIVOT(4,\"__count\",\"#employee_id\",10)"}, "G20": {"style": 8, "content": "=_t(\"Total\")", "border": 2}, "G21": {"style": 7, "content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",1)"}, "G22": {"content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",2)"}, "G23": {"style": 7, "content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",3)"}, "G24": {"content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",4)"}, "G25": {"style": 7, "content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",5)"}, "G26": {"content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",6)"}, "G27": {"style": 7, "content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",7)"}, "G28": {"content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",8)"}, "G29": {"style": 7, "content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",9)"}, "G30": {"content": "=ODOO.PIVOT(1,\"total_amount\",\"#product_id\",10)"}, "G33": {"style": 8, "content": "=_t(\"Total\")", "border": 2}, "G34": {"style": 7, "content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",1)"}, "G35": {"content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",2)"}, "G36": {"style": 7, "content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",3)"}, "G37": {"content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",4)"}, "G38": {"style": 7, "content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",5)"}, "G39": {"content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",6)"}, "G40": {"style": 7, "content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",7)"}, "G41": {"content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",8)"}, "G42": {"style": 7, "content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",9)"}, "G43": {"content": "=ODOO.PIVOT(4,\"total_amount\",\"#employee_id\",10)"}, "A8": {"border": 2}, "B7": {"border": 1}, "B8": {"border": 2}, "B19": {"border": 1}, "B32": {"border": 1}, "C7": {"border": 1}, "C8": {"border": 2}, "C19": {"border": 1}, "C32": {"border": 1}, "D7": {"border": 1}, "D8": {"border": 2}, "E7": {"border": 1}, "E8": {"border": 2}, "F7": {"border": 1}, "F8": {"border": 2}, "F19": {"border": 1}, "F32": {"border": 1}, "G7": {"border": 1}, "G8": {"border": 2}, "G19": {"border": 1}, "G32": {"border": 1}}, "conditionalFormats": [], "figures": [{"id": "6ce637db-4068-457f-91a4-ce83736932ff", "x": 0, "y": 178, "width": 1000, "height": 230, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "right", "metaData": {"groupBy": ["date:month", "product_id"], "measure": "total_amount", "order": null, "resModel": "hr.expense"}, "searchParams": {"comparison": null, "context": {}, "domain": [], "groupBy": ["date:month", "product_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true}}, {"id": "34d7af0c-d66d-46f5-9b4f-91a8f26be506", "x": 0, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "Expenses", "type": "scorecard", "keyValue": "Data!C1"}}, {"id": "64e233df-0643-4205-86c8-d051c45326a4", "x": 210, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "To report", "type": "scorecard", "keyValue": "Data!C2"}}, {"id": "62c97109-f819-42a7-929d-e76a1204be62", "x": 420, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "To validate", "type": "scorecard", "keyValue": "Data!C3"}}, {"id": "a84056d3-f845-4555-9bd9-3281b8b1c872", "x": 630, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "To reimburse", "type": "scorecard", "keyValue": "Data!C4"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "44bfb206-c15d-4314-a394-97690f1d5e8e", "name": "Data", "colNumber": 26, "rowNumber": 98, "rows": {}, "cols": {"0": {"size": 117.33935546875}, "1": {"size": 94.33203125}, "2": {"size": 92.66455078125}, "3": {"size": 147.4892578125}, "4": {"size": 60.9326171875}, "5": {"size": 192.05615234375}, "6": {"size": 175.32373046875}, "7": {"size": 146.36572265625}, "8": {"size": 53.22021484375}, "9": {"size": 105.18212890625}, "10": {"size": 65.43310546875}}, "merges": [], "cells": {"A1": {"content": "KPI - Expenses"}, "A2": {"content": "KPI - To report"}, "A3": {"content": "KPI - To validate"}, "A4": {"content": "KPI - To reimburse"}, "B1": {"content": "=ODOO.PIVOT(5,\"__count\")"}, "B2": {"content": "=ODOO.PIVOT(6,\"total_amount\")"}, "B3": {"content": "=ODOO.PIVOT(7,\"total_amount\")"}, "B4": {"content": "=ODOO.PIVOT(8,\"total_amount\")"}, "C1": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B1)"}, "C2": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B2)"}, "C3": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B3)"}, "C4": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B4)"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"bold": true}, "3": {"fillColor": "#f8f9fa", "textColor": "#01666b"}, "4": {"textColor": "#01666b"}, "5": {"fillColor": "#f8f9fa", "textColor": "#741b47"}, "6": {"textColor": "#741b47"}, "7": {"fillColor": "#f8f9fa"}, "8": {"bold": true, "align": "right"}}, "formats": {"1": "0", "2": "#,##0[$€]"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "f6ef6888-c8ce-42bf-9775-349332cb4f2f", "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ","}}, "chartOdooMenusReferences": {"6ce637db-4068-457f-91a4-ce83736932ff": "hr_expense.menu_hr_expense_root", "34d7af0c-d66d-46f5-9b4f-91a8f26be506": "hr_expense.menu_hr_expense_all_expenses", "64e233df-0643-4205-86c8-d051c45326a4": "hr_expense.menu_hr_expense_all_expenses", "62c97109-f819-42a7-929d-e76a1204be62": "hr_expense.menu_hr_expense_all_expenses", "a84056d3-f845-4555-9bd9-3281b8b1c872": "hr_expense.menu_hr_expense_all_expenses"}, "odooVersion": 4, "lists": {"1": {"columns": ["date", "name", "employee_id", "payment_mode", "activity_ids", "company_id", "nb_attachment", "total_amount", "state"], "domain": [], "model": "hr.expense", "context": {}, "orderBy": [{"name": "total_amount", "asc": false}], "id": "1", "name": "Top Expenses"}}, "listNextId": 2, "pivots": {"1": {"colGroupBys": [], "context": {}, "domain": [["product_id", "!=", false]], "id": "1", "measures": [{"field": "__count"}, {"field": "total_amount"}], "model": "hr.expense", "rowGroupBys": ["product_id"], "name": "Top Categories", "sortedColumn": {"groupId": [[], []], "measure": "total_amount", "order": "desc"}}, "4": {"colGroupBys": [], "context": {}, "domain": [["employee_id", "!=", false]], "id": "4", "measures": [{"field": "__count"}, {"field": "total_amount"}], "model": "hr.expense", "rowGroupBys": ["employee_id"], "name": "Top Employees", "sortedColumn": {"groupId": [[], []], "measure": "total_amount", "order": "desc"}}, "5": {"colGroupBys": [], "context": {}, "domain": [], "id": "5", "measures": [{"field": "__count"}], "model": "hr.expense", "rowGroupBys": [], "name": "KPI - Expenses", "sortedColumn": {"groupId": [[], []], "measure": "total_amount", "order": "desc"}}, "6": {"colGroupBys": [], "context": {}, "domain": [["sheet_id", "=", false]], "id": "6", "measures": [{"field": "total_amount"}], "model": "hr.expense", "rowGroupBys": [], "name": "KPI - To report", "sortedColumn": {"groupId": [[], []], "measure": "total_amount", "order": "desc"}}, "7": {"colGroupBys": [], "context": {}, "domain": [["state", "=", "reported"]], "id": "7", "measures": [{"field": "total_amount"}], "model": "hr.expense", "rowGroupBys": [], "name": "KPI - To validate", "sortedColumn": {"groupId": [[], []], "measure": "total_amount", "order": "desc"}}, "8": {"colGroupBys": [], "context": {}, "domain": [["state", "=", "approved"]], "id": "8", "measures": [{"field": "total_amount"}], "model": "hr.expense", "rowGroupBys": [], "name": "KPI - To reimburse", "sortedColumn": {"groupId": [[], []], "measure": "total_amount", "order": "desc"}}, "9": {"colGroupBys": [], "context": {"params": {"action": 952, "model": "hr.expense", "view_type": "pivot", "menu_id": 659, "cids": 1}}, "domain": [["sale_order_id", "!=", false]], "id": "9", "measures": [{"field": "__count"}, {"field": "total_amount"}], "model": "hr.expense", "rowGroupBys": ["sale_order_id"], "name": "Expenses Analysis by Customer to Reinvoice", "sortedColumn": {"groupId": [[], []], "measure": "total_amount", "order": "desc"}}}, "pivotNextId": 10, "globalFilters": [{"id": "54e94f8d-739d-4a02-8205-c25be895720e", "type": "date", "label": "Period", "defaultValue": "last_year", "rangeType": "relative", "defaultsToCurrentPeriod": true, "pivotFields": {"1": {"field": "date", "type": "date", "offset": 0}, "4": {"field": "date", "type": "date", "offset": 0}, "5": {"field": "date", "type": "date", "offset": 0}, "6": {"field": "date", "type": "date", "offset": 0}, "7": {"field": "date", "type": "date", "offset": 0}, "8": {"field": "date", "type": "date", "offset": 0}}, "listFields": {"1": {"field": "date", "type": "date", "offset": 0}}, "graphFields": {"6ce637db-4068-457f-91a4-ce83736932ff": {"field": "date", "type": "date", "offset": 0}}}, {"id": "0e80d79f-8175-422a-bdda-f5e491a6a201", "type": "relation", "label": "Product", "modelName": "product.product", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "product_id", "type": "many2one"}, "4": {"field": "product_id", "type": "many2one"}, "5": {"field": "product_id", "type": "many2one"}, "6": {"field": "product_id", "type": "many2one"}, "7": {"field": "product_id", "type": "many2one"}, "8": {"field": "product_id", "type": "many2one"}, "9": {"field": "product_id", "type": "many2one"}}, "listFields": {"1": {"field": "product_id", "type": "many2one"}}, "graphFields": {"6ce637db-4068-457f-91a4-ce83736932ff": {"field": "product_id", "type": "many2one"}}}, {"id": "e1b40389-7f46-44c0-acee-52033634fc5f", "type": "relation", "label": "Order", "modelName": "sale.order", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "sale_order_id", "type": "many2one"}, "4": {"field": "sale_order_id", "type": "many2one"}, "5": {"field": "sale_order_id", "type": "many2one"}, "6": {"field": "sale_order_id", "type": "many2one"}, "7": {"field": "sale_order_id", "type": "many2one"}, "8": {"field": "sale_order_id", "type": "many2one"}, "9": {"field": "sale_order_id", "type": "many2one"}}, "listFields": {"1": {"field": "sale_order_id", "type": "many2one"}}, "graphFields": {"6ce637db-4068-457f-91a4-ce83736932ff": {"field": "sale_order_id", "type": "many2one"}}}, {"id": "88fbcc54-dcb5-42ec-86e9-1676c4c73c6e", "type": "relation", "label": "Employee", "modelName": "hr.employee", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "employee_id", "type": "many2one"}, "4": {"field": "employee_id", "type": "many2one"}, "5": {"field": "employee_id", "type": "many2one"}, "6": {"field": "employee_id", "type": "many2one"}, "7": {"field": "employee_id", "type": "many2one"}, "8": {"field": "employee_id", "type": "many2one"}, "9": {"field": "employee_id", "type": "many2one"}}, "listFields": {"1": {"field": "employee_id", "type": "many2one"}}, "graphFields": {"6ce637db-4068-457f-91a4-ce83736932ff": {"field": "employee_id", "type": "many2one"}}}]}