# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_expense
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "# Expenses"
msgstr "# Despesas"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Category"
msgstr "Categoria"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "Funcionário"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expense"
msgstr "Despesa"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_expense.spreadsheet_dashboard_expense
#, python-format
msgid "Expenses"
msgstr "Despesas"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis"
msgstr "Análise de Despesas"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis by Customer to Reinvoice"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - Expenses"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To report"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To validate"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Order"
msgstr "Ordem"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Período"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produto"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To report"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To validate"
msgstr "Por Validar"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr "Categorias Principais"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Employees"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Expenses"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Reinvoiced Orders"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Total"
msgstr "Total"
