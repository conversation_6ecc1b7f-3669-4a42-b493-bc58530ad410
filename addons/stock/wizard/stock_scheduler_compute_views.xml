<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_procurement_compute_wizard" model="ir.ui.view">
        <field name="name">Run Schedulers Manually</field>
        <field name="model">stock.scheduler.compute</field>
        <field name="arch" type="xml">
            <form string="Parameters">
                <p>
                The stock will be reserved for operations waiting for availability and the reordering rules will be triggered.
                </p>
                <footer>
                    <button name="procure_calculation" string="Run Scheduler" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x" />
                </footer>
            </form>
        </field>
    </record>

    <record id="action_procurement_compute" model="ir.actions.act_window">
        <field name="name">Run Scheduler</field>
        <field name="res_model">stock.scheduler.compute</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <menuitem action="action_procurement_compute" id="menu_procurement_compute" parent="menu_stock_warehouse_mgmt" sequence="135" groups="base.group_no_one"/>


</odoo>
