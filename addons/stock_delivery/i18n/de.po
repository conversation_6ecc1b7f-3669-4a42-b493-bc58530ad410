# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_delivery
# 
# Translators:
# <PERSON><PERSON>, 2025
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/sale_order.py:0
#, python-format
msgid " (Estimated Cost: %s )"
msgstr " (Geschätzte Kosten: %s )"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "(berechnet:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span> - Gewicht (geschätzt):</span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span>  - Gewicht: </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>Versandgewicht: </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Carrier:</strong>"
msgstr "<strong>Transportunternehmen:</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>HS Code</strong>"
msgstr "<strong>HS-Code</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Shipping Weight: </strong>"
msgstr "<strong>Versandgewicht: </strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                    <br/>"
msgstr ""
"<strong>Versandgewicht:</strong>\n"
"                    <br/>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid ""
"<strong>Total Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Gesamtgewicht:</strong>\n"
"                <br/>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Tracking Number:</strong>"
msgstr "<strong>Sendungsverfolgungsnr.:</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Weight: </strong>"
msgstr "<strong>Gewicht: </strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid ""
"<strong>Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Gewicht:</strong>\n"
"                <br/>"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_route__shipping_selectable
msgid "Applicable on Shipping Methods"
msgstr "Auf Versandmethoden anwendbar"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Schüttgewicht"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid ""
"Cancelling a delivery may not be undoable. Are you sure you want to "
"continue?"
msgstr ""
"Die Stornierung einer Lieferung ist möglicherweise nicht rückgängig zu "
"machen. Sind Sie sicher, dass Sie fortfahren möchten?"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier"
msgstr "Transportunternehmen"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "Code des Transportunternehmens"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier name"
msgstr "Name des Transportunternehmens"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Cost: %(price).2f %(currency)s"
msgstr "Kosten: %(price).2f %(currency)s"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_uid
msgid "Created by"
msgstr "Angelegt von"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_date
msgid "Created on"
msgstr "Angelegt am"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Assistent zur Auswahl von Zustellern"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Auswahlassistent für Lieferpakete"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "Lieferung Pakettyp"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.vpicktree_view_tree
msgid "Destination"
msgstr "Zielort"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "Zielland"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "Verwerfen"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: stock_delivery
#: model:ir.actions.act_window,name:stock_delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "Sendungsverfolgungslinks anzeigen"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"Geschätzte Kosten: Dem Kunden werden die geschätzten Kosten für den Versand in Rechnung gestellt.\n"
"Tatsächliche Kosten: Dem Kunden werden die tatsächlichen Kosten für den Versand in Rechnung gestellt, die Kosten für den Versand werden nach der Lieferung auf dem Verkaufsauftrag aktualisiert."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Exception occurred with respect to carrier on the transfer"
msgstr "Ausnahme in Bezug auf Zusteller im Transfer"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Exception:"
msgstr "Ausnahme:"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.product_template_hs_code
msgid "HS Code"
msgstr "HS-Code"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__id
msgid "ID"
msgstr "ID"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_route
msgid "Inventory Routes"
msgstr "Lagerrouten"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Abrechnungspolitik"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "Ist Retourenkommissionierung"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Manual actions might be needed."
msgstr "Manuelle Aktionen können erforderlich sein."

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "Keine Integration des Transportunternehmens"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__country_of_origin
msgid "Origin of Goods"
msgstr "Herkunft Güter"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "Paketreferenz"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Package Details"
msgstr "Paketdetails"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid "Package too heavy!"
msgstr "Paket zu schwer!"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_quant_package
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__package_ids
msgid "Packages"
msgstr "Pakete"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "Kommissionierung"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "Rücksendeetikett drucken"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produktbewegungen (Lagerbuchung)"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__delivery_type
msgid "Provider"
msgstr "Anbieter"

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "Tatsächliche Kosten"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "Rücksendeetikett"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "Rücksendung"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__route_ids
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_delivery_carrier_form_inherit_stock_delivery
msgid "Routes"
msgstr "Routen"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,help:stock_delivery.field_product_template__country_of_origin
msgid ""
"Rules of origin determine where goods originate, i.e. not where they have been shipped from, but where they have been produced or manufactured.\n"
"As such, the ‘origin’ is the 'economic nationality' of goods traded in commerce."
msgstr ""
"Die Ursprungsregeln bestimmen, woher die Waren stammen, d. h. nicht, woher sie versandt wurden, sondern wo sie produziert oder hergestellt wurden.\n"
"Der „Ursprung“ ist somit die „wirtschaftliche Nationalität“ der im Handel befindlichen Waren."

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "Verkaufspreis"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkaufsauftragszeile"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "Speichern"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "An Zusteller senden"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s"
msgstr ""
"Die Sendung mit der Sendungsnummer %(ref)s wurde an das Transportunternehmen"
" %(carrier_name)s übergeben."

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "Versandkosten"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "Versandinformationen"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_delivery_carrier
#: model:ir.ui.menu,name:stock_delivery.menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_location_route_view_form_inherit_stock_delivery
msgid "Shipping Methods"
msgstr "Versandmethoden"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__shipping_weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Versandgewicht"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Shipping Weight:"
msgstr "Versandgewicht:"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,help:stock_delivery.field_product_template__hs_code
msgid ""
"Standardized code for international shipping and goods declaration. At the "
"moment, only used for FedEx and USPS shipping providers."
msgstr ""
"Standardisierter Code für den internationalen Versand und die "
"Warendeklaration. Im Moment nur für FedEx- und USPS-Versanddienstleister "
"verwendet."

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move
msgid "Stock Move"
msgstr "Bestandsbewegung"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_package_type
msgid "Stock package type"
msgstr "Typ des Lagerpakets"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_rounding
msgid "Technical field indicating weight's number of decimal places"
msgstr ""
"Technisches Feld zur Angabe der Anzahl der Dezimalstellen des Gewichts"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_is_kg
msgid "Technical field indicating whether weight uom is kg or not (i.e. lb)"
msgstr ""
"Technisches Feld, das angibt, ob die Gewichtseinheit kg ist oder nicht (d.h."
" lb)"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO-Ländercode in zwei Zeichen. \n"
"Sie können dieses Feld auch für eine Schnellsuche einsetzen."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
#: code:addons/stock_delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr ""
"Das Paket kann nicht erstellt werden, da das Gesamtgewicht der Produkte in "
"der Kommissionierung 0,0 beträgt %s"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "The shipping price will be set once the delivery is done."
msgstr "Der Versandpreis wird nach erfolgter Lieferung festgelegt."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""
"Das Gewicht Ihres Pakets ist höher als das für diesen Pakettyp zulässige "
"Höchstgewicht. Bitte wählen Sie einen anderen Pakettyp."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
#, python-format
msgid "There is no matching delivery rule."
msgstr "Es gibt keine passende Versandregel."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "Gesamtgewicht aller in dem Paket enthaltenen Produkte."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"Gesamtgewicht von Paketen und Produkten, die nicht in einem Paket enthalten "
"sind. Bei Paketen, für die kein Versandgewicht angegeben ist, wird "
"standardmäßig das Gesamtgewicht der Produkte verwendet. Dies ist das "
"Gewicht, das zur Berechnung der Versandkosten verwendet wird."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "Gesamtgewicht der Produkte, die sich nicht in einem Paket befinden."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Gesamtgewicht des Pakets."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "Gesamtgewicht der Produkte in der Kommissionierung."

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "Verfolgungs-URL"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Sendungsverfolgung"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "Sendungsverfolgungsnummer"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "Verfolgungs-URL"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid "Tracking links for shipment:"
msgstr "Sendungsverfolgungslinks für Sendung:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
msgid "Tracking:"
msgstr "Sendungsverfolgung:"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Gewicht"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Gewicht für Versand"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "Gewicht für Versand"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Bezeichnung der Gewichtsmaßeinheit"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Weight:"
msgstr "Gewicht:"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot pack products into the same package when they have different "
"carriers (i.e. check that all of their transfers have a carrier assigned and"
" are using the same carrier)."
msgstr ""
"Sie können keine Produkte in ein und dasselbe Paket packen, wenn sie "
"unterschiedliche Transportunternehmen haben (d. h. überprüfen Sie, ob allen "
"Ihren Transfers ein Transportunternehmen zugewiesen ist und ob sie dasselbe "
"Transportunternehmen verwenden)."

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr "Sie haben mehrere Verfolgungslinks, die im Chatter verfügbar sind."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""
"Ihre Liefermethode bietet keine Angabe zur Website des "
"Versanddienstleisters, um Ihre Bestellung zu verfolgen."

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "PLZ-Präfix"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDShipping Weight:"
msgstr "^A0N,44,33^FDVersandgewicht:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDWeight:"
msgstr "^A0N,44,33^FDGewicht:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FO310,200"
msgstr "^FO310,200"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FS"
msgstr "^FS"
