# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_landed_costs
#
# Translators:
# Qaidjohar <PERSON>bha<PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Qaidjohar Barbhaya, 2023\n"
"Language-Team: Gujarati (https://app.transifex.com/odoo/teams/41243/gu/)\n"
"Language: gu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid " already out"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.stock_landed_cost_view_kanban
msgid "<i class=\"fa fa-clock-o\" title=\"Date\" role=\"img\" aria-label=\"Date\"/>"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_account_move_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__account_id
msgid "Account"
msgstr "Account"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_journal_id
msgid "Account Journal"
msgstr "Account Journal"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction
msgid "Action Needed"
msgstr "Action Needed"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_ids
msgid "Activities"
msgstr "Activities"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activity Exception Decoration"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_state
msgid "Activity State"
msgstr "Activity State"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Activity Type Icon"
msgstr "Activity Type Icon"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Additional Costs"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__additional_landed_cost
msgid "Additional Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__target_model
msgid "Apply On"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_attachment_count
msgid "Attachment Count"
msgstr "Attachment Count"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_current_cost_price
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_current_cost_price
msgid "By Current Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_quantity
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_quantity
msgid "By Quantity"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_volume
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_volume
msgid "By Volume"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_weight
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_weight
msgid "By Weight"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cancel"
msgstr "Cancel"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__cancel
msgid "Cancelled"
msgstr "Cancelled"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_company
msgid "Companies"
msgstr "Companies"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__company_id
msgid "Company"
msgstr "Company"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__company_id
msgid "Company related to this journal"
msgstr "Company related to this journal"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Compute"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_config_settings
msgid "Config Settings"
msgstr "Config Settings"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__price_unit
msgid "Cost"
msgstr "પડતર-કિંમત"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_line_id
msgid "Cost Line"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__cost_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cost Lines"
msgstr ""

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Cost and adjustments lines do not match. You should maybe recompute the landed costs."
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
msgid "Create Landed Costs"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.action_stock_landed_cost
msgid "Create a new landed cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_uid
msgid "Created by"
msgstr "Created by"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_date
msgid "Created on"
msgstr "Created on"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__currency_id
msgid "Currency"
msgstr "Currency"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__date
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Date"
msgstr "Date"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_config_settings__lc_journal_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.res_config_settings_view_form
msgid "Default Journal"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method when used for Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__name
msgid "Description"
msgstr "Description"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__display_name
msgid "Display Name"
msgstr "Display Name"

#. module: stock_landed_costs
#: model:mail.message.subtype,name:stock_landed_costs.mt_stock_landed_cost_open
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Done"
msgstr "Done"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__draft
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Draft"
msgstr "Draft"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__equal
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__equal
msgid "Equal"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid ""
"Equal: Cost will be equally divided.\n"
"By Quantity: Cost will be divided according to product's quantity.\n"
"By Current cost: Cost will be divided according to product's current cost.\n"
"By Weight: Cost will be divided depending on its weight.\n"
"By Volume: Cost will be divided depending on its volume."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_follower_ids
msgid "Followers"
msgstr "Followers"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_partner_ids
msgid "Followers (Partners)"
msgstr "Followers (Partners)"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon e.g. fa-tasks"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Future Activities"
msgstr "Future Activities"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Group By"
msgstr "Group By"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__has_message
msgid "Has Message"
msgstr "Has Message"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__id
msgid "ID"
msgstr "ID"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon to indicate an exception activity."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction
msgid "If checked, new messages require your attention."
msgstr "If checked, new messages require your attention."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "If checked, some messages have a delivery error."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__landed_cost_ok
msgid "Indicates whether the product is a landed cost: when receiving a vendor bill, you can allocate this cost on preceding receipts."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_is_follower
msgid "Is Follower"
msgstr "Is Follower"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__is_landed_costs_line
msgid "Is Landed Costs Line"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__landed_cost_ok
msgid "Is a Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__description
msgid "Item Description"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Journal"
msgstr "Journal"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_move_id
msgid "Journal Entry"
msgstr "Journal Entry"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move_line
msgid "Journal Item"
msgstr "Journal Item"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_layer__stock_landed_cost_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.actions.act_window,name:stock_landed_costs.action_stock_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_ids
#: model:ir.ui.menu,name:stock_landed_costs.menu_stock_landed_cost
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree2
msgid "Landed Costs"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_visible
msgid "Landed Costs Visible"
msgstr ""

#. module: stock_landed_costs
#: model:mail.message.subtype,description:stock_landed_costs.mt_stock_landed_cost_open
msgid "Landed cost validated"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.action_stock_landed_cost
msgid "Landed costs allow you to include additional costs (shipment, insurance, customs duties, etc) into the cost of the product."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Late Activities"
msgstr "Late Activities"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_company__lc_journal_id
msgid "Lc Journal"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error
msgid "Message Delivery error"
msgstr "Message Delivery error"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_ids
msgid "Messages"
msgstr "Messages"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "My Activity Deadline"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__name
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Name"
msgstr "Name"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "New"
msgstr "નવું"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__final_cost
msgid "New Value"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Next Activity Calendar Event"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Next Activity Deadline"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_summary
msgid "Next Activity Summary"
msgstr "Next Activity Summary"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_id
msgid "Next Activity Type"
msgstr "Next Activity Type"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of Actions"
msgstr "Number of Actions"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of errors"
msgstr "Number of errors"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Number of messages which requires an action"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Number of messages with delivery error"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Only draft landed costs can be validated"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__former_cost
msgid "Original Value"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Picking"
msgstr ""

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please configure Stock Expense Account for product: %s."
msgstr ""

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please define %s on which those additional costs should apply."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__done
msgid "Posted"
msgstr "Posted"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_product_template
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__product_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__product_id
msgid "Product"
msgstr "Product"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__product_type
msgid "Product Type"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_purchase_order_line
msgid "Purchase Order Line"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__quantity
msgid "Quantity"
msgstr "Quantity"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__rating_ids
msgid "Ratings"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_user_id
msgid "Responsible User"
msgstr "Responsible User"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Delivery error"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Show all records which has next action date is before today"
msgstr "Show all records which has next action date is before today"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid "Split Method"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__state
msgid "State"
msgstr "અવસ્થા"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Status"
msgstr "Status"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost
msgid "Stock Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost_lines
msgid "Stock Landed Cost Line"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__move_id
msgid "Stock Move"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Today Activities"
msgstr "Today Activities"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__amount_total
msgid "Total"
msgstr "Total"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__picking_ids
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__target_model__picking
msgid "Transfers"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type of the exception activity on record."

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Validate"
msgstr ""

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Validated landed costs cannot be cancelled, but you could create negative landed costs to reverse them"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_adjustment_lines
msgid "Valuation Adjustment Lines"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__valuation_adjustment_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Valuation Adjustments"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__vendor_bill_id
msgid "Vendor Bill"
msgstr "Vendor Bill"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__volume
msgid "Volume"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website Messages"
msgstr "Website Messages"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website communication history"
msgstr "Website communication history"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__weight
msgid "Weight"
msgstr ""

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "You cannot apply landed costs on the chosen %s(s). Landed costs can only be applied for products with FIFO or average costing method."
msgstr ""

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/product.py:0
#, python-format
msgid "You cannot change the product type or disable landed cost option because the product is used in an account move line."
msgstr ""
