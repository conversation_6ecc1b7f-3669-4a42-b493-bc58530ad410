# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: utm
#. odoo-python
#: code:addons/utm/models/utm_source.py:0
#, python-format
msgid "%(content)s (%(model_description)s created on %(create_date)s)"
msgstr "%(content)s (%(model_description)s створено %(create_date)s)"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__active
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "Активно"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr "Дозволяє нам фільтрувати релевантні кампанії"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approval-based Flow"
msgstr "Процес на основі схвалення"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approved"
msgstr "Затверджено"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Archive"
msgstr "Архів"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Archived"
msgstr "Заархівовано"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Assign tags to your campaigns to organize, filter and track them."
msgstr ""
"Призначайте теги своїм кампаніям, щоб упорядковувати, фільтрувати та "
"відстежувати їх."

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Audience-driven Flow"
msgstr "Процес, орієнтований на аудиторію"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr "Автоматично створена кампанія"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr "Кампанія"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
msgid "Campaign Identifier"
msgstr "Ідентифікатор кампанії"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__title
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "Campaign Name"
msgstr "Назва кампанії"

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr "Етап кампанії"

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr "Теги кампанії"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr "Кампанії"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Кампанії використовуються для централізації ваших маркетингових зусиль і "
"відстеження їх результатів."

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns that are assigned to me"
msgstr "Кампанії, призначені мені"

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "Спеціальні різдвяні"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Collect ideas, design creative content and publish it once reviewed."
msgstr ""
"Збирайте ідеї, створюйте креативний контент і публікуйте його після "
"перевірки."

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "Індекс кольору"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Копірайтинг"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Create a Medium"
msgstr "Створити канал"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Create a Tag"
msgstr "Створити тег"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Create a campaign"
msgstr "Створити кампанію"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr "Створіть етап вашої кампанії"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "Створив"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "Створено"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Creative Flow"
msgstr "Процес створення"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "Видалити"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deploy"
msgstr "Розгорнути"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deployed"
msgstr "Завантажено"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_2
#, python-format
msgid "Design"
msgstr "Дизайн"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Done"
msgstr "Виконано"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Edit"
msgstr "Редагувати"

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr "Кампанія електронної пошти - Товари"

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "Електронна пошта - послуги"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Event-driven Flow"
msgstr "Процес на основі подій"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Gather Data"
msgstr "Збирайте дані"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Gather data, build a recipient list and write content based on your "
"Marketing target."
msgstr ""
"Збирайте дані, створюйте список одержувачів і пишіть вміст на основі вашої "
"маркетингової цілі."

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "Групувати за"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизація HTTP"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "ID"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Ідеї"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Later"
msgstr "Пізніше"

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "Відстеження посилань"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "List-Building"
msgstr "Створення списку"

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr "Маркетинг"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "Середній"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr "Назва каналу"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr "Канали"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "My Campaigns"
msgstr "Мої кампанії"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source_mixin__name
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
msgid "Name"
msgstr "Ім'я"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "Новий"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "No Sources yet!"
msgstr "Ще немає джерел!"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Pre-Launch"
msgstr "Попередній запуск"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Prepare Campaigns and get them approved before making them go live."
msgstr "Підготуйте кампанії та отримайте їх схвалення, перш ніж запускати їх."

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Prepare your Campaign, test it with part of your audience and deploy it "
"fully afterwards."
msgstr ""
"Підготуйте свою кампанію, протестуйте її з частиною своєї аудиторії та "
"повністю розгорніть її після цього."

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Report"
msgstr "Звіт"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "Відповідальний"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Restore"
msgstr "Відновлення"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Review"
msgstr "Огляд"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Running"
msgstr "Діючий"

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "Продаж"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr "Запланувати"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr "Пошук каналу UTM"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Send"
msgstr "Надіслати"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_3
#, python-format
msgid "Sent"
msgstr "Надіслано"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch"
msgstr "Легкий запуск"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch Flow"
msgstr "Процес легкого запуску"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model:ir.model.fields,field_description:utm.field_utm_source_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr "Джерело"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "Назва джерела"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "Джерела"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr "Стадія"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr "Етапи"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. plan, design, in progress,"
" done, …)."
msgstr ""
"Етапи дозволяють організувати ваш робочий процес (наприклад, заплановано, "
"дизайн, у процесі, готово, …)."

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_tag__color
msgid ""
"Tag color. No color means no display in kanban to distinguish internal tags "
"from public categorization tags."
msgstr ""
"Колір тегу. Відсутність кольору означає відсутність відображення в канбані, "
"щоб відрізнити внутрішні теги від загальнодоступних тегів категоризації."

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Такий тег уже існує!"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Tags"
msgstr "Мітки"

#. module: utm
#. odoo-python
#: code:addons/utm/models/utm_medium.py:0
#, python-format
msgid ""
"The UTM medium '%s' cannot be deleted as it is used in some main functional "
"flows, such as the recruitment and the mass mailing."
msgstr ""
"Медіа UTM '%s' не можна видалити, оскільки використовується у деяких "
"основних процесах, таких як рекрутинг та масова розсилка."

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_campaign_unique_name
#: model:ir.model.constraint,message:utm.constraint_utm_medium_unique_name
#: model:ir.model.constraint,message:utm.constraint_utm_source_unique_name
msgid "The name must be unique"
msgstr "Назва має бути унікальною"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Month"
msgstr "Цього місяця"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Week"
msgstr "Цього тижня"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Це ім'я, яке допомагає вам стежити за різними зусиллями кампанії, напр. "
"Fall_Drive, Christmas_Special"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Це спосіб доставки, наприклад, листівка, електронна пошта або банерна "
"реклама"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Це джерело посилання, наприклад, пошукова система, інший домен або ім'я "
"списку листів"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "To be Approved"
msgstr "Буде затверджено"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Track incoming events (e.g. Christmas, Black Friday, ...) and publish timely"
" content."
msgstr ""
"Відстежуйте найближчі події (наприклад, Різдво, Чорна п’ятниця, ...) і "
"своєчасно публікуйте вміст."

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr "Кампанія UTM"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr "Кампанії UTM"

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr "Канал UTM "

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid ""
"UTM Mediums track the mean that was used to attract traffic (e.g. "
"\"Website\", \"Twitter\", ...)."
msgstr ""
"Канали UTM відстежують значення, яке було використано для залучення трафіку "
"(наприклад, «Веб-сайт», «Твіттер», ...)."

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr "Мікс UTM "

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr "Джерело UTM "

#. module: utm
#: model:ir.model,name:utm.model_utm_source_mixin
msgid "UTM Source Mixin"
msgstr "Джерело UTM Mixin"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid ""
"UTM Sources track where traffic comes from  (e.g. \"May Newsletter\", \"\", "
"...)."
msgstr ""
"Джерела UTM відстежують, звідки надходить трафік (наприклад, \"Травень "
"інформаційний бюлетень\", \"\", ...)."

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr "Етапи UTM"

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr "Тег UTM"

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTM"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Use This For My Campaigns"
msgstr "Використовуйте це для Моєї кампанії"

#. module: utm
#. odoo-python
#: code:addons/utm/models/utm_source.py:0
#, python-format
msgid ""
"You cannot update multiple records with the same name. The name should be "
"unique!"
msgstr ""
"Ви не можете оновити кілька записів з однаковою назвою. Назва має бути "
"унікальною!"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_form
msgid "e.g. \"Brainstorming\""
msgstr "напр. \"Брейншторм\""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "e.g. \"Christmas Mailing\""
msgstr "напр. \"Різдвяна розсилка\""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "e.g. \"Email\""
msgstr "напр. \"Email\""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "e.g. Black Friday"
msgstr "напр., Чорна П'ятниця"
