<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="shape" width="800" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M.0025.2235.8425.05.8425.85.0025.85Z">
            <animate dur="6s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M.0025.2235.8425.05.8425.85.0025.85Z;
            M.0025.2035.8425.075.8425.85.0025.85Z;
            M.0025.2235.8425.05.8425.85.0025.85Z"
            calcMode="spline"
            keySplines=".56 .37 .43 .58; .56 .37 .43 .58"/>
        </path>
        <g id="animation">
            <animateTransform xlink:href="#shape" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;0 8;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform xlink:href="#filterPath" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 -.02;0 0;0 -.02" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
        </g>
    </defs>
    <svg class="triangle" viewBox="0 0 28.35 28.35" width="95%" height="90%">
        <path id="triangle" d="M28.35,28.35H0V0Z" fill="#7C6576"/>
    </svg>
    <svg id="dots" viewBox="0 0 71.53 49.13" width="45%" height="45%" x="53%" y="53%">
        <rect x="5.39" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="13.83" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="22.28" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="30.73" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="39.18" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="47.63" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="56.08" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="64.53" y="3.7" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="5.39" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="13.83" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="22.28" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="30.73" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="39.18" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="47.63" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="56.08" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="64.53" y="13.73" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="5.39" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="13.83" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="22.28" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="30.73" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="39.18" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="47.63" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="56.08" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="64.53" y="23.76" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="5.39" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="13.83" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="22.28" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="30.73" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="39.18" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="47.63" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="56.08" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="64.53" y="33.78" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="5.39" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="13.83" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="22.28" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="30.73" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="39.18" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="47.63" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="56.08" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
        <rect x="64.53" y="43.81" width="1.62" height="1.62" fill="#383E45"/>
    </svg>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
