<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 1400 1080" data-forced-size="true" width="1400" height="1080" data-img-aspect-ratio="4:3" data-img-perspective="[[28.43, 2.43], [97.35, 26], [72.34, 96.1], [1.98, 64.24]]">
    <defs>
        <linearGradient id="gradient_01" x1="-623.45" y1="525.96" x2="774.32" y2="554.15" gradientTransform="matrix(-1, 0, 0, 1, 782.63, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.2" stop-color="#8a8a8a"/>
            <stop offset="0.32" stop-color="#2d2d2d"/>
            <stop offset="0.39" stop-color="#a1a1a1"/>
            <stop offset="0.48" stop-color="#8a8a8a"/>
            <stop offset="0.54" stop-color="#787878"/>
            <stop offset="0.87" stop-color="#646464"/>
            <stop offset="0.93" stop-color="#414141"/>
            <stop offset="0.97" stop-color="#2c2c2c"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="light_adjust" x1="-623.45" y1="525.96" x2="774.32" y2="554.15" gradientTransform="matrix(-1, 0, 0, 1, 782.63, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="0.5" stop-color="#fff" stop-opacity=".5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <linearGradient id="gradient_02" x1="-1925.99" y1="-643.38" x2="-1924.15" y2="-648.14" gradientTransform="matrix(1.6, -0.66, -2.07, -2.4, 2987.15, -2152.34)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <radialGradient id="gradient_03" cx="-2556.22" cy="1155.2" r="2.66" gradientTransform="translate(-2600.75 511.4) rotate(-159.06)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#6d7f86"/>
            <stop offset="0.05" stop-color="#5c6c76"/>
            <stop offset="0.15" stop-color="#414e5b"/>
            <stop offset="0.26" stop-color="#2b3445"/>
            <stop offset="0.37" stop-color="#1a2134"/>
            <stop offset="0.51" stop-color="#0e1328"/>
            <stop offset="0.68" stop-color="#070b21"/>
            <stop offset="1" stop-color="#05091f"/>
        </radialGradient>
        <linearGradient id="gradient_04" x1="132.68" y1="494.47" x2="1988.37" y2="-251.92" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="397.21 19.62 22.89 698.81 1026.55 1055.16 1384.53 276.26 397.21 19.62"/>
        </clipPath>
        <path id="filterPath" d="M0.9889,0.2538l-0.2557,0.7212L0.0163,0.6451,0.2837,0.0162Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1359.27,243.9C1315.89,232.56,427.36,1.1,427.36,1.1c-20.83-5.4-54.09,10-64.35,27.48C353.86,44.21,17.2,650.06,6.21,669.35S-.52,710.11,13.74,725c0,0,13.54,13.56,19.76,16.32S963.82,1070.88,980.82,1077s65.08,6.92,84.7-35.81S1392.43,328,1397.68,316.37,1402.66,255.24,1359.27,243.9Zm-3,51.44c-3.42,7.47-326.85,706.84-335,723.65s-19.38,15.14-25.39,13S47.66,701,39.68,697.77s-6.94-12.13-3.83-18S388.5,43.31,391.92,37.61s9.33-10.27,23-6.85c0,0,919.19,242.49,931.11,245.71S1359.72,287.88,1356.3,295.34Z" fill="url(#gradient_01)"/>
        <path d="M13.74,725s13.54,13.56,19.76,16.32S963.82,1070.88,980.82,1077s65.08,6.92,84.7-35.81S1392.43,328,1397.68,316.36s5-61.12-38.41-72.46S427.36,1.1,427.36,1.1c-20.83-5.4-54.09,10-64.35,27.48C353.86,44.21,17.2,650.06,6.21,669.35S-.53,710.11,13.74,725ZM201.86,348.48c-1.79,1.79-4.29,2.19-5.59.89s-.89-3.8.9-5.59,4.29-2.19,5.59-.89S203.65,346.69,201.86,348.48Zm-172.15,333c3.11-5.91,356.22-643.9,359.64-649.61s9.33-10.27,23-6.84c0,0,928.43,246.29,940.34,249.5s13.68,11.41,10.26,18.88-330.13,716-338.31,732.75-19.38,15.14-25.39,13S41.52,702.73,33.54,699.52,26.6,687.38,29.71,681.47Z" fill="url(#light_adjust)" opacity="0.2" style="mix-blend-mode: difference"/>
        <path d="M1359.41,247.5,425.5,3.48C410.68.16,377.72,9.08,364.67,31.9,350.58,56.52,16.37,657,7.66,673.19s-3.21,47,18.24,54.45,941,331.91,955,336.67c17.24,5.88,56.89-.21,71.4-29S1383,317.06,1390.5,300.74,1386.91,254.69,1359.41,247.5Zm-3.11,47.84c-3.42,7.47-326.85,706.84-335,723.65s-19.38,15.14-25.39,13S47.66,701,39.68,697.77s-6.94-12.13-3.83-18S388.5,43.31,391.92,37.61s9.33-10.27,23-6.85c0,0,919.19,242.49,931.11,245.71S1359.72,287.88,1356.3,295.34Z"/>
        <path d="M9.47,719.92c4.24,5,9.71,9.87,16.22,12.18,14.3,5.08,911.88,323.29,950.15,335.6s68.4-5.81,80.83-31.12c5.89-12,329-715.37,334.8-727.41s5.68-30.69-2.76-45.42c-6.29-8.83-15.7-16.26-29.44-19.85C1315.89,232.56,427.36,1.1,427.36,1.1c-20.83-5.4-54.09,10-64.35,27.48C353.86,44.21,17.2,650.06,6.21,669.35-3.58,686.55-1.24,705.46,9.47,719.92ZM7.66,673.19C16.37,657,350.58,56.52,364.67,31.9,377.72,9.08,410.68.16,425.5,3.48l933.91,244c27.5,7.19,38.55,36.92,31.09,53.24s-323.74,705.83-338.25,734.6-54.16,34.85-71.4,29c-14-4.76-933.5-329.2-954.95-336.67S-1,689.37,7.66,673.19Z" fill="#fff" opacity="0.5"/>
        <g id="details">
            <path d="M1225.26,680.79a23.8,23.8,0,0,1,2-9.2l9.61-21.51c1.32-2.94,3.22-4.16,3.73-2.29s-.07,5.32-1.45,8.48l-10.67,24C1227,683.59,1225.34,683.75,1225.26,680.79Z" fill="#3d3d3d"/>
            <path d="M1224.72,682a22.48,22.48,0,0,1,2-9l10.14-22.65c1.26-2.86,3.11-4,3.59-2.22s-.06,5.16-1.46,8.2l-11.21,25.19C1226.4,684.72,1224.79,684.87,1224.72,682Z" fill="url(#gradient_02)"/>
            <path d="M1225.21,681.2a20.8,20.8,0,0,1,1.76-8l9.48-21.39c1.16-2.58,2.81-3.65,3.2-1.93s-.08,4.65-1.26,7.33L1228,680.71C1226.72,683.6,1225.28,683.77,1225.21,681.2Z" fill="#131313"/>
            <g>
                <path d="M1288.44,541.51a11.09,11.09,0,0,0-1.23,5c.14,1.09.93.49,1.75-1.24a10.45,10.45,0,0,0,1.09-4.72C1289.93,539.54,1289.21,540,1288.44,541.51Z" fill="#000102"/>
                <path d="M1288.94,545.27c-.8,1.74-1.59,2.34-1.75,1.24l2.22-2.43A7.35,7.35,0,0,1,1288.94,545.27Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1289.89,542.46a13.89,13.89,0,0,1-.93,2.77l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1288.19,542.52a9.44,9.44,0,0,0-1,4.13c.13.87.77.4,1.44-1a9,9,0,0,0,.94-3.92C1289.43,540.82,1288.82,541.2,1288.19,542.52Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1292.89,531.83a11,11,0,0,0-1.24,5c.14,1.08.93.49,1.75-1.24a10.66,10.66,0,0,0,1.1-4.73C1294.37,529.86,1293.65,530.28,1292.89,531.83Z" fill="#000102"/>
                <path d="M1293.38,535.6c-.8,1.74-1.59,2.33-1.75,1.24l2.23-2.44A7.9,7.9,0,0,1,1293.38,535.6Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1294.33,532.79a13.89,13.89,0,0,1-.93,2.77l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1292.63,532.84a9.65,9.65,0,0,0-1,4.14c.13.86.77.39,1.45-1.05A9.15,9.15,0,0,0,1294,532C1293.87,531.15,1293.26,531.53,1292.63,532.84Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1297.33,522.16a11.12,11.12,0,0,0-1.24,5c.14,1.09.93.5,1.75-1.23a10.55,10.55,0,0,0,1.1-4.73C1298.81,520.19,1298.1,520.61,1297.33,522.16Z" fill="#000102"/>
                <path d="M1297.82,525.92c-.8,1.75-1.59,2.34-1.75,1.24l2.23-2.43A7.41,7.41,0,0,1,1297.82,525.92Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1298.77,523.11a13.89,13.89,0,0,1-.93,2.77l-1.44.53.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1297.07,523.17a9.61,9.61,0,0,0-1,4.13c.14.87.77.4,1.45-1a9,9,0,0,0,.93-3.93C1298.31,521.47,1297.7,521.85,1297.07,523.17Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1301.77,512.49a11.25,11.25,0,0,0-1.24,5c.15,1.09.94.49,1.75-1.24a10.35,10.35,0,0,0,1.1-4.72C1303.26,510.52,1302.54,510.94,1301.77,512.49Z" fill="#000102"/>
                <path d="M1302.26,516.25c-.79,1.74-1.58,2.34-1.75,1.24l2.23-2.43A6.59,6.59,0,0,1,1302.26,516.25Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1303.21,513.44a13.24,13.24,0,0,1-.92,2.77l-1.45.52.35-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1301.51,513.5a9.55,9.55,0,0,0-1,4.13c.13.87.76.4,1.44-1a9.15,9.15,0,0,0,.94-3.92C1302.75,511.8,1302.14,512.18,1301.51,513.5Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1306.21,502.81a11.1,11.1,0,0,0-1.23,5c.14,1.08.93.49,1.75-1.24a10.49,10.49,0,0,0,1.09-4.73C1307.7,500.84,1307,501.26,1306.21,502.81Z" fill="#000102"/>
                <path d="M1306.71,506.58c-.8,1.74-1.59,2.33-1.75,1.24l2.22-2.44A6.92,6.92,0,0,1,1306.71,506.58Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1307.66,503.76a13.76,13.76,0,0,1-.93,2.78l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1306,503.82a9.51,9.51,0,0,0-1,4.14c.13.86.77.39,1.44-1a9,9,0,0,0,.94-3.93C1307.2,502.13,1306.59,502.51,1306,503.82Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1310.66,493.14a11.12,11.12,0,0,0-1.24,5c.14,1.09.93.5,1.75-1.23a10.68,10.68,0,0,0,1.1-4.73C1312.14,491.17,1311.42,491.59,1310.66,493.14Z" fill="#000102"/>
                <path d="M1311.15,496.9c-.8,1.75-1.59,2.34-1.75,1.24l2.23-2.43A7.41,7.41,0,0,1,1311.15,496.9Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1312.1,494.09a13.89,13.89,0,0,1-.93,2.77l-1.44.53.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1310.4,494.15a9.61,9.61,0,0,0-1,4.13c.13.87.77.4,1.45-1a9.18,9.18,0,0,0,.93-3.93C1311.64,492.45,1311,492.83,1310.4,494.15Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1315.1,483.47a11.12,11.12,0,0,0-1.24,5c.14,1.08.93.49,1.75-1.24a10.48,10.48,0,0,0,1.1-4.72C1316.58,481.5,1315.87,481.92,1315.1,483.47Z" fill="#000102"/>
                <path d="M1315.59,487.23c-.8,1.74-1.59,2.33-1.75,1.24l2.23-2.43A7.41,7.41,0,0,1,1315.59,487.23Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1316.54,484.42a13.89,13.89,0,0,1-.93,2.77l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1314.84,484.48a9.58,9.58,0,0,0-1,4.13c.14.87.77.4,1.45-1.05a9,9,0,0,0,.93-3.92C1316.08,482.78,1315.47,483.16,1314.84,484.48Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1319.54,473.79a11.26,11.26,0,0,0-1.24,5c.15,1.08.94.49,1.75-1.24a10.39,10.39,0,0,0,1.1-4.73C1321,471.82,1320.31,472.24,1319.54,473.79Z" fill="#000102"/>
                <path d="M1320,477.56c-.79,1.74-1.58,2.33-1.75,1.24l2.23-2.44A7,7,0,0,1,1320,477.56Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1321,474.74a13.32,13.32,0,0,1-.92,2.78l-1.45.52L1319,477Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1319.28,474.8a9.62,9.62,0,0,0-1,4.14c.13.86.76.39,1.44-1.05a9.19,9.19,0,0,0,.94-3.93C1320.52,473.11,1319.91,473.49,1319.28,474.8Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1324,464.12a11.09,11.09,0,0,0-1.23,5c.14,1.09.93.5,1.75-1.23a10.51,10.51,0,0,0,1.09-4.73C1325.47,462.15,1324.75,462.57,1324,464.12Z" fill="#000102"/>
                <path d="M1324.48,467.88c-.8,1.75-1.59,2.34-1.75,1.24l2.22-2.43A6.53,6.53,0,0,1,1324.48,467.88Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1325.43,465.07a13.89,13.89,0,0,1-.93,2.77l-1.44.53.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1323.73,465.13a9.47,9.47,0,0,0-1,4.13c.13.87.77.4,1.44-1a9.07,9.07,0,0,0,.94-3.93C1325,463.43,1324.36,463.81,1323.73,465.13Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1328.43,454.45a11.12,11.12,0,0,0-1.24,5c.14,1.08.93.49,1.75-1.24a10.62,10.62,0,0,0,1.1-4.72C1329.91,452.48,1329.19,452.9,1328.43,454.45Z" fill="#000102"/>
                <path d="M1328.92,458.21c-.8,1.74-1.59,2.33-1.75,1.24l2.23-2.43A7.41,7.41,0,0,1,1328.92,458.21Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1329.87,455.4a13.89,13.89,0,0,1-.93,2.77l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1328.17,455.46a9.58,9.58,0,0,0-1,4.13c.13.87.77.4,1.45-1a9.14,9.14,0,0,0,.93-3.92C1329.41,453.76,1328.8,454.14,1328.17,455.46Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1332.87,444.77a11.13,11.13,0,0,0-1.24,5c.14,1.08.93.49,1.75-1.24a10.52,10.52,0,0,0,1.1-4.73C1334.35,442.8,1333.64,443.22,1332.87,444.77Z" fill="#000102"/>
                <path d="M1333.36,448.54c-.8,1.74-1.59,2.33-1.75,1.23l2.23-2.43A7.9,7.9,0,0,1,1333.36,448.54Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1334.31,445.72a14,14,0,0,1-.93,2.78l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1332.61,445.78a9.65,9.65,0,0,0-1,4.14c.14.86.77.39,1.45-1.05a9,9,0,0,0,.93-3.93C1333.85,444.09,1333.24,444.47,1332.61,445.78Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1337.31,435.1a11.25,11.25,0,0,0-1.24,5c.15,1.09.94.5,1.75-1.23a10.41,10.41,0,0,0,1.1-4.73C1338.8,433.13,1338.08,433.55,1337.31,435.1Z" fill="#000102"/>
                <path d="M1337.8,438.86c-.79,1.75-1.58,2.34-1.75,1.24l2.23-2.43A6.59,6.59,0,0,1,1337.8,438.86Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1338.75,436.05a13.24,13.24,0,0,1-.92,2.77l-1.45.53.35-1.07Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1337.05,436.11a9.58,9.58,0,0,0-1,4.13c.13.87.76.4,1.44-1.05a9.15,9.15,0,0,0,.94-3.92C1338.29,434.41,1337.68,434.79,1337.05,436.11Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1341.75,425.43a11.09,11.09,0,0,0-1.23,5c.14,1.08.93.49,1.75-1.24a10.45,10.45,0,0,0,1.09-4.72C1343.24,423.45,1342.52,423.88,1341.75,425.43Z" fill="#000102"/>
                <path d="M1342.25,429.19c-.8,1.74-1.59,2.33-1.75,1.24l2.22-2.44A6.35,6.35,0,0,1,1342.25,429.19Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1343.2,426.38a13.89,13.89,0,0,1-.93,2.77l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1341.5,426.43a9.51,9.51,0,0,0-1,4.14c.13.87.77.4,1.44-1.05a9,9,0,0,0,.94-3.93C1342.74,424.74,1342.13,425.12,1341.5,426.43Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1346.2,415.75a11.13,11.13,0,0,0-1.24,5c.14,1.08.93.49,1.75-1.24a10.66,10.66,0,0,0,1.1-4.73C1347.68,413.78,1347,414.2,1346.2,415.75Z" fill="#000102"/>
                <path d="M1346.69,419.52c-.8,1.74-1.59,2.33-1.75,1.23l2.23-2.43A7.9,7.9,0,0,1,1346.69,419.52Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1347.64,416.7a14,14,0,0,1-.93,2.78l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1345.94,416.76a9.65,9.65,0,0,0-1,4.14c.13.86.77.39,1.45-1a9.18,9.18,0,0,0,.93-3.93C1347.18,415.07,1346.57,415.45,1345.94,416.76Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1350.64,406.08a11.12,11.12,0,0,0-1.24,5c.15,1.09.93.5,1.75-1.23a10.55,10.55,0,0,0,1.1-4.73C1352.12,404.11,1351.41,404.53,1350.64,406.08Z" fill="#000102"/>
                <path d="M1351.13,409.84c-.8,1.75-1.58,2.34-1.75,1.24l2.23-2.43A7.41,7.41,0,0,1,1351.13,409.84Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1352.08,407a13.89,13.89,0,0,1-.93,2.77l-1.44.53.34-1.07Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1350.38,407.09a9.61,9.61,0,0,0-1,4.13c.14.87.77.4,1.45-1.05a9,9,0,0,0,.93-3.92C1351.62,405.39,1351,405.77,1350.38,407.09Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1355.08,396.41a11.25,11.25,0,0,0-1.24,5c.15,1.08.94.49,1.75-1.24a10.35,10.35,0,0,0,1.1-4.72C1356.57,394.43,1355.85,394.86,1355.08,396.41Z" fill="#000102"/>
                <path d="M1355.57,400.17c-.79,1.74-1.58,2.33-1.75,1.24l2.23-2.44A6.39,6.39,0,0,1,1355.57,400.17Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1356.52,397.36a13.24,13.24,0,0,1-.92,2.77l-1.45.52.35-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1354.82,397.41a9.62,9.62,0,0,0-1,4.14c.13.87.76.4,1.44-1.05a9.19,9.19,0,0,0,.94-3.93C1356.06,395.72,1355.45,396.1,1354.82,397.41Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1359.52,386.73a11.1,11.1,0,0,0-1.23,5c.14,1.08.93.49,1.75-1.24a10.51,10.51,0,0,0,1.09-4.73C1361,384.76,1360.29,385.18,1359.52,386.73Z" fill="#000102"/>
                <path d="M1360,390.5c-.8,1.74-1.59,2.33-1.75,1.23l2.22-2.43A6.92,6.92,0,0,1,1360,390.5Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1361,387.68a14.19,14.19,0,0,1-.93,2.78l-1.44.52.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1359.27,387.74a9.51,9.51,0,0,0-1,4.14c.13.86.77.39,1.44-1.05a9.07,9.07,0,0,0,.94-3.93C1360.51,386.05,1359.9,386.43,1359.27,387.74Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1100.28,951.33a11.57,11.57,0,0,0-1.28,5.2c.15,1.12,1,.51,1.82-1.29a10.9,10.9,0,0,0,1.13-4.9C1101.82,949.28,1101.07,949.72,1100.28,951.33Z" fill="#000102"/>
                <path d="M1100.8,955.23c-.83,1.81-1.65,2.42-1.82,1.29l2.31-2.53A8,8,0,0,1,1100.8,955.23Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1101.78,952.31a15.21,15.21,0,0,1-1,2.88l-1.5.54.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1100,952.37a10,10,0,0,0-1.07,4.29c.14.9.8.41,1.5-1.09a9.45,9.45,0,0,0,1-4.07C1101.3,950.61,1100.67,951,1100,952.37Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1104.87,941.29a11.57,11.57,0,0,0-1.27,5.2c.15,1.12,1,.51,1.81-1.29a10.76,10.76,0,0,0,1.13-4.9C1106.41,939.24,1105.67,939.68,1104.87,941.29Z" fill="#000102"/>
                <path d="M1105.39,945.19c-.82,1.81-1.64,2.42-1.81,1.29l2.3-2.53A7.09,7.09,0,0,1,1105.39,945.19Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1106.37,942.27a13.53,13.53,0,0,1-1,2.88l-1.49.54.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1104.61,942.34a9.78,9.78,0,0,0-1.06,4.28c.14.9.79.42,1.5-1.08a9.48,9.48,0,0,0,1-4.08C1105.89,940.58,1105.26,941,1104.61,942.34Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1109.47,931.25a11.6,11.6,0,0,0-1.28,5.2c.15,1.13,1,.51,1.82-1.28a11.1,11.1,0,0,0,1.13-4.91C1111,929.2,1110.26,929.64,1109.47,931.25Z" fill="#000102"/>
                <path d="M1110,935.15c-.82,1.81-1.64,2.43-1.81,1.29l2.31-2.53A7.15,7.15,0,0,1,1110,935.15Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1111,932.23a14.08,14.08,0,0,1-1,2.88l-1.5.55.36-1.11Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1109.2,932.3a10.07,10.07,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.45,9.45,0,0,0,1-4.08C1110.49,930.54,1109.85,930.93,1109.2,932.3Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1114.06,921.21a11.6,11.6,0,0,0-1.28,5.2c.16,1.13,1,.51,1.82-1.28a11,11,0,0,0,1.13-4.91C1115.6,919.17,1114.85,919.6,1114.06,921.21Z" fill="#000102"/>
                <path d="M1114.58,925.11c-.83,1.81-1.64,2.43-1.82,1.29l2.31-2.53A7.6,7.6,0,0,1,1114.58,925.11Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1115.56,922.2a14.24,14.24,0,0,1-1,2.87l-1.5.55.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1113.8,922.26a10,10,0,0,0-1.07,4.29c.14.9.8.41,1.5-1.09a9.33,9.33,0,0,0,1-4.07C1115.08,920.5,1114.45,920.89,1113.8,922.26Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1118.65,911.17a11.61,11.61,0,0,0-1.27,5.21c.15,1.12,1,.51,1.81-1.29a10.82,10.82,0,0,0,1.13-4.9C1120.19,909.13,1119.45,909.57,1118.65,911.17Z" fill="#000102"/>
                <path d="M1119.17,915.08c-.82,1.81-1.64,2.42-1.81,1.28l2.3-2.52A6.65,6.65,0,0,1,1119.17,915.08Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1120.15,912.16a13.92,13.92,0,0,1-1,2.88l-1.49.54.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1118.39,912.22a9.81,9.81,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.44,9.44,0,0,0,1-4.07C1119.67,910.46,1119,910.86,1118.39,912.22Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1123.25,901.14a11.57,11.57,0,0,0-1.28,5.2c.15,1.12,1,.51,1.82-1.29a11,11,0,0,0,1.13-4.9C1124.79,899.09,1124,899.53,1123.25,901.14Z" fill="#000102"/>
                <path d="M1123.76,905c-.82,1.81-1.64,2.42-1.81,1.28l2.31-2.52A7.48,7.48,0,0,1,1123.76,905Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1124.75,902.12a15.21,15.21,0,0,1-1,2.88l-1.5.54.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1123,902.18a10.1,10.1,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.44,9.44,0,0,0,1-4.07C1124.27,900.42,1123.63,900.82,1123,902.18Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1127.84,891.1a11.41,11.41,0,0,0-1.27,5.2c.15,1.12,1,.51,1.81-1.29a10.76,10.76,0,0,0,1.13-4.9C1129.38,889.05,1128.63,889.49,1127.84,891.1Z" fill="#000102"/>
                <path d="M1128.36,895c-.83,1.81-1.64,2.42-1.82,1.29l2.31-2.53A8,8,0,0,1,1128.36,895Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1129.34,892.08a14.32,14.32,0,0,1-1,2.88l-1.49.54.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1127.58,892.14a9.81,9.81,0,0,0-1.06,4.29c.13.9.79.41,1.49-1.09a9.3,9.3,0,0,0,1-4.07C1128.86,890.38,1128.23,890.78,1127.58,892.14Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1132.43,881.06a11.57,11.57,0,0,0-1.27,5.2c.15,1.13,1,.51,1.81-1.29a10.76,10.76,0,0,0,1.13-4.9C1134,879,1133.23,879.45,1132.43,881.06Z" fill="#000102"/>
                <path d="M1133,885c-.82,1.81-1.64,2.43-1.81,1.29l2.3-2.53A6.39,6.39,0,0,1,1133,885Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1133.93,882a14.08,14.08,0,0,1-1,2.88l-1.5.55.35-1.11Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1132.17,882.11a10.07,10.07,0,0,0-1.06,4.29c.14.89.8.41,1.5-1.09a9.45,9.45,0,0,0,1-4.08C1133.45,880.35,1132.82,880.74,1132.17,882.11Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1137,871a11.6,11.6,0,0,0-1.28,5.2c.15,1.13,1,.51,1.82-1.28a11,11,0,0,0,1.13-4.91C1138.57,869,1137.82,869.41,1137,871Z" fill="#000102"/>
                <path d="M1137.55,874.92c-.83,1.81-1.65,2.43-1.82,1.29l2.31-2.53A8,8,0,0,1,1137.55,874.92Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1138.53,872a14.89,14.89,0,0,1-1,2.87l-1.5.55.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1136.76,872.07a10.07,10.07,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.45,9.45,0,0,0,1-4.08C1138.05,870.31,1137.42,870.7,1136.76,872.07Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1141.62,861a11.57,11.57,0,0,0-1.27,5.2c.15,1.13,1,.52,1.81-1.28a10.83,10.83,0,0,0,1.13-4.91C1143.16,858.94,1142.42,859.37,1141.62,861Z" fill="#000102"/>
                <path d="M1142.14,864.89c-.83,1.81-1.64,2.42-1.82,1.28l2.31-2.52A8.39,8.39,0,0,1,1142.14,864.89Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1143.12,862a14.24,14.24,0,0,1-1,2.87l-1.49.55.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1141.36,862a9.81,9.81,0,0,0-1.06,4.29c.13.9.79.41,1.49-1.09a9.33,9.33,0,0,0,1-4.07C1142.64,860.27,1142,860.66,1141.36,862Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1146.22,851a11.44,11.44,0,0,0-1.28,5.2c.15,1.12,1,.51,1.81-1.29a10.82,10.82,0,0,0,1.13-4.9C1147.75,848.9,1147,849.34,1146.22,851Z" fill="#000102"/>
                <path d="M1146.73,854.85c-.82,1.81-1.64,2.42-1.81,1.28l2.3-2.52A6.65,6.65,0,0,1,1146.73,854.85Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1147.71,851.93a14.49,14.49,0,0,1-1,2.88l-1.5.54.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1146,852a10.1,10.1,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.44,9.44,0,0,0,1-4.07C1147.24,850.23,1146.6,850.63,1146,852Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1150.81,840.91a11.57,11.57,0,0,0-1.28,5.2c.15,1.12,1,.51,1.82-1.29a10.9,10.9,0,0,0,1.13-4.9C1152.35,838.86,1151.6,839.3,1150.81,840.91Z" fill="#000102"/>
                <path d="M1151.33,844.81c-.83,1.81-1.65,2.42-1.82,1.29l2.31-2.53A8,8,0,0,1,1151.33,844.81Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1152.31,841.89a15.21,15.21,0,0,1-1,2.88l-1.5.54.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1150.54,842a10.1,10.1,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.45,9.45,0,0,0,1-4.07C1151.83,840.19,1151.2,840.59,1150.54,842Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1155.4,830.87a11.57,11.57,0,0,0-1.27,5.2c.15,1.12,1,.51,1.81-1.29a10.76,10.76,0,0,0,1.13-4.9C1156.94,828.82,1156.2,829.26,1155.4,830.87Z" fill="#000102"/>
                <path d="M1155.92,834.77c-.82,1.81-1.64,2.42-1.81,1.29l2.3-2.53A7.09,7.09,0,0,1,1155.92,834.77Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1156.9,831.85a13.53,13.53,0,0,1-1,2.88l-1.49.54.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1155.14,831.92a9.78,9.78,0,0,0-1.06,4.28c.14.9.79.42,1.5-1.08a9.48,9.48,0,0,0,1-4.08C1156.42,830.16,1155.79,830.55,1155.14,831.92Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1160,820.83a11.47,11.47,0,0,0-1.28,5.2c.15,1.13,1,.51,1.81-1.28a11,11,0,0,0,1.14-4.91C1161.53,818.78,1160.79,819.22,1160,820.83Z" fill="#000102"/>
                <path d="M1160.51,824.73c-.82,1.81-1.64,2.43-1.81,1.29l2.31-2.53A7.15,7.15,0,0,1,1160.51,824.73Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1161.49,821.81a14.08,14.08,0,0,1-1,2.88l-1.5.55.35-1.11Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1159.73,821.88a10.07,10.07,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.45,9.45,0,0,0,1-4.08C1161,820.12,1160.38,820.51,1159.73,821.88Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1164.59,810.79a11.6,11.6,0,0,0-1.28,5.2c.15,1.13,1,.51,1.82-1.28a11,11,0,0,0,1.13-4.91C1166.13,808.75,1165.38,809.18,1164.59,810.79Z" fill="#000102"/>
                <path d="M1165.11,814.69c-.83,1.81-1.65,2.43-1.82,1.29l2.31-2.53A7.6,7.6,0,0,1,1165.11,814.69Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1166.09,811.78a14.89,14.89,0,0,1-1,2.87l-1.5.55.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1164.33,811.84a10,10,0,0,0-1.07,4.29c.14.9.8.41,1.5-1.09a9.33,9.33,0,0,0,1-4.07C1165.61,810.08,1165,810.47,1164.33,811.84Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1169.18,800.75a11.61,11.61,0,0,0-1.27,5.21c.15,1.12,1,.51,1.81-1.29a10.82,10.82,0,0,0,1.13-4.9C1170.72,798.71,1170,799.15,1169.18,800.75Z" fill="#000102"/>
                <path d="M1169.7,804.66c-.82,1.81-1.64,2.42-1.81,1.28l2.3-2.52A6.65,6.65,0,0,1,1169.7,804.66Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1170.68,801.74a13.92,13.92,0,0,1-1,2.88l-1.49.54.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1168.92,801.8a9.81,9.81,0,0,0-1.06,4.29c.14.9.79.41,1.5-1.09a9.44,9.44,0,0,0,1-4.07C1170.2,800,1169.57,800.44,1168.92,801.8Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1173.78,790.72a11.57,11.57,0,0,0-1.28,5.2c.15,1.12,1,.51,1.82-1.29a11,11,0,0,0,1.13-4.9C1175.32,788.67,1174.57,789.11,1173.78,790.72Z" fill="#000102"/>
                <path d="M1174.29,794.62c-.82,1.81-1.64,2.42-1.81,1.28l2.31-2.52A7.48,7.48,0,0,1,1174.29,794.62Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1175.28,791.7a15.21,15.21,0,0,1-1,2.88l-1.5.54.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1173.51,791.76a10.1,10.1,0,0,0-1.06,4.29c.14.9.8.41,1.5-1.09a9.44,9.44,0,0,0,1-4.07C1174.8,790,1174.16,790.4,1173.51,791.76Z" fill="#0a0e0e"/>
            </g>
            <g>
                <ellipse cx="199.52" cy="346.13" rx="4.66" ry="3.37" transform="translate(-184.11 234.58) rotate(-43.83)" fill="#1a1c1c"/>
                <ellipse cx="199.51" cy="346.14" rx="3.04" ry="2.21" transform="translate(-184.12 234.56) rotate(-43.82)" fill="url(#gradient_03)"/>
            </g>
        </g>
        <path d="M13.74,725s13.54,13.56,19.76,16.32S963.82,1070.88,980.82,1077s65.08,6.92,84.7-35.81S1392.43,328,1397.68,316.36s5-61.12-38.41-72.46S427.36,1.1,427.36,1.1c-20.83-5.4-54.09,10-64.35,27.48C353.86,44.21,17.2,650.06,6.21,669.35S-.53,710.11,13.74,725ZM201.86,348.48c-1.79,1.79-4.29,2.19-5.59.89s-.89-3.8.9-5.59,4.29-2.19,5.59-.89S203.65,346.69,201.86,348.48Zm-172.15,333c3.11-5.91,356.22-643.9,359.64-649.61s9.33-10.27,23-6.84c0,0,928.43,246.29,940.34,249.5s13.68,11.41,10.26,18.88-330.13,716-338.31,732.75-19.38,15.14-25.39,13S41.52,702.73,33.54,699.52,26.6,687.38,29.71,681.47Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
        <path d="M1320.34,373.14c21.54-46.59,35.27-76.29,36-77.8,3.42-7.46,1.66-15.66-10.26-18.87S414.93,30.76,414.93,30.76c-13.68-3.42-19.59,1.14-23,6.85C389.33,41.92,188.7,403.74,89.77,582.3Z" opacity="0.4" fill="url(#gradient_04)"/>
    </g>
</svg>
