<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="600" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M0,0.0427C0,0.0191,0.0191,0,0.0427,0H0.4384C0.462,0,0.4811,0.0191,0.4811,0.0427V0.9573C0.4811,0.9809,0.462,1,0.4384,1H0.0427C0.0191,1,0,0.9809,0,0.9573V0.0427ZM0.5189,0.0427C0.5189,0.0191,0.538,0,0.5616,0H0.9573C0.9809,0,1,0.0191,1,0.0427V0.9573C1,0.9809,0.9809,1,0.9573,1H0.5616C0.538,1,0.5189,0.9809,0.5189,0.9573V0.0427Z">
        </path>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
