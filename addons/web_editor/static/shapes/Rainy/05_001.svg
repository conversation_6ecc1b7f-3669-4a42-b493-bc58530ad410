<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1400">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:url(#SVGID_2_);}
	.st2{fill:#3AADAA;}
</style>
<defs>
	<mask maskUnits="userSpaceOnUse" x="0" y="0" width="1400" height="1400" id="SVGID_3_">
		<rect style="fill:white" width="1400" height="1400"/>
		<path class="st2" d="M0,0h605.4v1.4l0-0.4h794.5l0.1,192.6c0,10.4-8.4,18.7-18.8,18.8h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-7.5  c0-10.4-8.4-18.8-18.7-18.8h-0.4c-10.3,0-18.7,8.4-18.7,18.8v40c0,10.4-8.4,18.7-18.8,18.8h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-2.8  c0-10.4-8.4-18.8-18.7-18.8h-0.4c-8.9,0-16.5,6.3-18.4,14.9v56c0,10.4-8.4,18.7-18.7,18.8h-0.4c-10.4,0-18.7-8.4-18.7-18.8  l-0.4-68.5c-1.9-8.7-9.5-14.9-18.3-14.9h-0.4c-10.3,0-18.7,8.4-18.7,18.8v44.1c0,10.4-8.4,18.8-18.7,18.8h-0.4  c-10.4,0-18.7-8.4-18.8-18.8v-15.6c0-10.4-8.4-18.7-18.7-18.8h-0.4c-10.3,0-18.7,8.4-18.7,18.8l0,0v48.8c0,10.4-8.4,18.8-18.7,18.8  h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-7.5c0-10.4-8.4-18.8-18.7-18.8h-0.4c-10.3,0-18.7,8.4-18.7,18.8v40c0,10.3-8.4,18.7-18.7,18.8  h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-2.8c0-10.4-8.4-18.7-18.7-18.8h-0.4c-8.9,0-16.5,6.3-18.4,14.9v56c0,10.4-8.4,18.7-18.7,18.8H927  c-10.3,0-18.7-8.4-18.7-18.8l-0.4-68.5c-1.9-8.7-9.5-14.9-18.3-14.9h-0.4c-10.4,0-18.7,8.4-18.8,18.8v44.1  c0,10.4-8.4,18.7-18.7,18.8h-0.5c-10.3-0.1-18.7-8.4-18.7-18.8v-15.6c0-10.4-8.4-18.8-18.7-18.8h-0.4c-9.2,0-17,6.7-18.5,15.7  l-0.3,58c0,10.4-8.4,18.7-18.7,18.8h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-56c-1.8-8.7-9.5-14.9-18.3-14.9h-0.4  c-10.3,0-18.7,8.4-18.7,18.8v2.9c0,10.4-8.4,18.8-18.7,18.8H700c-10.3,0-18.7-8.4-18.7-18.8v-40c0-10.4-8.4-18.8-18.7-18.8h-0.4  c-10.4,0-18.7,8.4-18.8,18.8v7.5c0,10.4-8.4,18.8-18.8,18.8h-0.4c-10.2,0-18.6-8.3-18.7-18.5v-2.9c0-10.4-8.4-18.8-18.7-18.8h-0.4  c-9.7,0-17.8,7.4-18.7,17.1c-0.1,0.6-0.1,17.5-0.1,17.5c0,10.4-8.4,18.8-18.7,18.8h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-44.1  c0-10.4-8.4-18.8-18.8-18.8h-0.4c-8.9,0-16.5,6.2-18.3,14.9l-0.4,68.5c0,10.3-8.4,18.7-18.7,18.8h-0.4c-10.3,0-18.7-8.4-18.7-18.8  v-56c-1.8-8.7-9.5-14.9-18.4-14.9h-0.4c-10.4,0-18.7,8.4-18.8,18.8v2.8c0,10.4-8.4,18.7-18.7,18.8h-0.4c-10.3,0-18.7-8.4-18.7-18.8  v-40c0-10.4-8.4-18.7-18.7-18.8h-0.3c-10.4,0-18.8,8.4-18.8,18.8v7.5c0,10.4-8.4,18.8-18.8,18.8h-0.4c-10.3,0-18.7-8.4-18.7-18.8  V210l0,0c0-10.4-8.4-18.7-18.7-18.8h-0.4c-10.3,0-18.7,8.4-18.7,18.8v15.6c0,10.3-8.4,18.7-18.7,18.8h-0.4  c-10.3,0-18.7-8.4-18.7-18.8v-44c0-10.4-8.4-18.7-18.7-18.8h-0.4c-8.8,0.1-16.4,6.3-18.3,14.9l-0.4,68.5c0,10.4-8.4,18.7-18.7,18.8  h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-56c-1.8-8.7-9.5-14.9-18.4-14.9h-0.4c-10.3,0-18.7,8.4-18.7,18.8v2.8c0,10.4-8.4,18.8-18.8,18.8  h-0.4c-10.3,0-18.7-8.4-18.7-18.8v-40c0-10.4-8.4-18.7-18.7-18.8h-0.4c-10.4,0-18.8,8.4-18.8,18.8v7.5c0,10.4-8.4,18.7-18.8,18.8  h-0.4C8.4,183.1,0,174.7,0,164.3L0,0z"/>
	</mask>
</defs>
<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="18645.6504" y1="-5838.8398" x2="18645.6504" y2="-6533.2598" gradientTransform="matrix(-1 0 0 1 19345.6504 6388.0703)">
	<stop offset="0" style="stop-color:#3AADAA;stop-opacity:0"/>
	<stop offset="1" style="stop-color:#3AADAA"/>
</linearGradient>
<g style="mask:url(#SVGID_3_)">
	<rect class="st0" width="1400" height="570"/>
</g>
<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="18640.3203" y1="-6558.2705" x2="18626.9336" y2="-7232.4897" gradientTransform="matrix(-1 0 0 1 19345.6504 6388.0698)">
	<stop offset="0" style="stop-color:white"/>
	<stop offset="1" style="stop-color:white;stop-opacity:0"/>
</linearGradient>
<path class="st2" d="M0,130.1c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1l0.5-76.8c2.1-9.7,10.7-16.7,20.6-16.7H64  c11.6,0.1,21,9.5,21,21.1V107c0,11.6,9.4,21,21,21h0.5c11.6-0.1,20.9-9.4,21-21V89.6c0-11.6,9.4-21,21-21.1h0.5  c11.6,0.1,21,9.5,21,21.1v54.6c0,11.6,9.4,21,21,21.1h0.5c11.6,0,21-9.5,21-21.1v-8.4c0-11.6,9.4-21,21-21.1h0.5  c11.6,0.1,21,9.5,21,21.1v44.8c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1v-3.1c0-11.6,9.4-21,21-21.1h0.5  c9.9,0,18.5,7,20.6,16.8v62.7c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1l0.5-76.8c2.1-9.8,10.8-16.8,20.8-16.7h0.5  c11.6,0.1,21,9.5,21,21.1v49.4c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1v-17.5c0-11.6,9.4-21,21-21.1h0.5  c10.3,0,19,7.5,20.7,17.6l0.4,65c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1v-26.2c2.1-9.7,10.6-16.7,20.6-16.8h0.5  c11.6,0.1,21,9.5,21,21.1v3.1c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1v-44.8c0-11.6,9.4-21,21-21.1h0.4  c11.6,0.1,21,9.5,21,21.1v8.4c0,11.6,9.4,21,21,21.1h0.5c11.5,0,20.8-9.3,21-20.7v-3.3c0-11.6,9.4-21,21-21.1h0.5  c10.9,0,19.9,8.3,20.9,19.1c0.1,0.7,0.1,19.6,0.1,19.6c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1v-49.4  c0-11.6,9.4-21,21-21.1h0.5c9.9,0,18.5,7,20.6,16.7l0.5,76.8c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1v-62.7  c2.1-9.7,10.6-16.7,20.6-16.8h0.5c11.6,0.1,21,9.5,21,21.1v3.1c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1v-44.8  c0-11.6,9.4-21,21-21.1h0.4c11.6,0.1,21,9.5,21,21.1v8.4c0,11.6,9.4,21,21,21.1h0.4c11.6-0.1,21-9.5,21-21.1V93.2l0,0  c0.1-11.6,9.4-20.9,21-21h0.4c11.6,0.1,20.9,9.4,21,21v17.5c0.1,11.6,9.4,20.9,21,21h0.5c11.6-0.1,20.9-9.4,21-21V61.3  c0-11.6,9.4-21,21-21.1h0.5c9.9,0,18.5,7,20.6,16.7l0.5,76.8c0,11.6,9.4,21,21,21.1h0.4c11.6-0.1,21-9.5,21-21.1V71  c2.1-9.7,10.6-16.7,20.6-16.7h0.5c11.6,0.1,21,9.5,21,21.1v3.1c0.1,11.6,9.4,20.9,21,21h0.5c11.6-0.1,20.9-9.4,21-21V33.7  c0-11.6,9.4-21,21-21.1h0.4c11.6,0.1,21,9.5,21,21.1v8.4c0,11.6,9.4,21,21,21.1h0.5c11.6-0.1,21-9.5,21-21.1V0H0.1L0,130.1z"/>
</svg>
