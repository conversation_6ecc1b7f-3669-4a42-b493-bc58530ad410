# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_unsplash
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Generate an Access Key"
msgstr ""

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr "Prieigos raktas"

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_app_id
#, python-format
msgid "Application ID"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "Apply"
msgstr "Taikyti"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_attachment
msgid "Attachment"
msgstr "Prisegtukas"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: web_unsplash
#. odoo-python
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash URL!"
msgstr ""

#. module: web_unsplash
#. odoo-python
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash notify URL!"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "Get an Access key"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "Paste your access key here"
msgstr "Čia įdėkite savo prieigos raktą"

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "Paste your application ID here"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "Photos (via Unsplash)"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Please check your Unsplash access key and application ID."
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "QWEB lauko paveikslėlis"

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Search is temporarily unavailable"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Setup Unsplash to access royalty free photos."
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Something went wrong"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Unauthorized Key"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Uploading %s '%s' images."
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.js:0
#, python-format
msgid "Uploading '%s' image."
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "User"
msgstr "Vartotojas"

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "and paste"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "and paste it here:"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "here:"
msgstr ""

#. module: web_unsplash
#. odoo-javascript
#: code:addons/web_unsplash/static/src/components/media_dialog/image_selector.xml:0
#, python-format
msgid "unsplash"
msgstr ""
