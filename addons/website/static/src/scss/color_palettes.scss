:root {
    // Override css variables to influence the default style of the editor
    // without duplicating the css.
    @include print-variable('o-we-toolbar-height', $o-navbar-height);

    // Need the colors, name, menu background color and footer background color of each color palette
    $o-palette-names: ();
    $-default-cc: ();
    $-bg-attrs: 'menu', 'footer';
    @each $attr in $-bg-attrs {
        // Since the variables built here are currently only used for theme previews we
        // decided to keep things simple and we deliberately ignore potential overrides
        // from #{$-bg-attrs}-custom and #{$-bg-attrs}-gradient.
        $-default-cc: map-merge($-default-cc, ($attr: map-get($o-base-color-palette, $attr)))
    }

    @each $-selected-palette-name in $o-selected-color-palettes-names {
        $-palette: map-get($o-color-palettes, $-selected-palette-name);

        @if $-selected-palette-name != 'user-palette' {
            @each $key, $value in $-palette {
                @include print-variable('o-palette-#{$-selected-palette-name}-#{$key}', $value);
            }

            @each $attr-name, $default-cc-idx in $-default-cc {
                $-cc-idx: $default-cc-idx;
                @if map-has-key($-palette, $attr-name) {
                    $-cc-idx: map-get($-palette, $attr-name);
                }
                $-bg: $body-bg;
                @if $-cc-idx != null {
                    $-cc: nth($o-color-combinations, $-cc-idx);
                    $-bg: o-safe-get($-palette, 'o-cc#{$-cc-idx}-bg', map-get($-cc, 'bg'));
                    @if type-of($-bg) != color {
                        $-bg: map-get($-palette, $-bg);
                    }
                }
                @include print-variable('o-palette-#{$-selected-palette-name}-#{$attr-name}-bg', $-bg);
            }
        }
    }

    // Need the palette colors used as default menu and footer background color
    @each $attr-name, $default-cc-idx in $-default-cc {
        $-cc: nth($o-color-combinations, $default-cc-idx);
        $-default-bg: map-get($-cc, 'bg');
        @include print-variable('o-default-#{$attr-name}-bg', $-default-bg);
    }

    @include print-variable('palette-names', $o-selected-color-palettes-names);

    // Need info about the base grays which are used to compute the final grays
    @each $name, $color in $o-base-gray-color-palette {
        @include print-variable('base-#{$name}', $color);
    }
}
