<?xml version="1.0"?>
<odoo>

<record id="view_blog_post_form" model="ir.ui.view">
    <field name="name">blog.post.form</field>
    <field name="model">blog.post</field>
    <field name="arch" type="xml">
        <form string="Blog Post">
            <sheet>
                <div class="oe_button_box" name="button_box" invisible="not active">
                    <field name="is_published" widget="website_redirect_button"/>
                </div>
                <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                <group name="blog_details">
                    <field name="blog_id"/>
                    <field name="active" invisible="1"/>
                    <field name="name" placeholder="Blog Post Title"/>
                    <field name="subtitle" placeholder="Blog Subtitle"/>
                    <field name="tag_ids" widget="many2many_tags"/>
                    <field name="website_id" groups="website.group_multi_website"/>
                </group>
                <group name="publishing_details" string="Publishing Options">
                    <field name="author_id"/>
                    <field name="create_date" groups="base.group_no_one"/>
                    <field name="visits"/>
                    <field name="post_date"/>
                    <field name="write_uid"/>
                    <field name="write_date"/>
                </group>
                <notebook>
                    <page name="seo" string="SEO" groups="base.group_no_one">
                        <group name="default_opengraph">
                            <field name="website_meta_title" string="Meta Title"/>
                            <field name="website_meta_description" string="Meta Description"/>
                            <field name="website_meta_keywords" string="Meta Keywords" help="Separate every keyword with a comma"/>
                        </group>
                    </page>
                </notebook>
            </sheet>
            <div class="oe_chatter">
                <field name="message_follower_ids" groups="base.group_user"/>
                <field name="message_ids"/>
            </div>
        </form>
    </field>
</record>

<record id="blog_post_view_kanban" model="ir.ui.view">
    <field name="name">blog.post.kanban</field>
    <field name="model">blog.post</field>
    <field name="arch" type="xml">
        <kanban js_class="website_pages_kanban" class="o_kanban_mobile" action="open_website_url" type="object" sample="1">
            <field name="name"/>
            <field name="blog_id"/>
            <field name="author_id"/>
            <field name="post_date"/>
            <field name="website_url"/>
            <templates>
                <t t-name="kanban-box">
                    <div class="oe_kanban_global_click d-flex flex-column">
                        <div class="row mb-auto">
                            <strong class="col-8">
                                <span class="o_text_overflow" t-esc="record.name.value"/>
                                <div class="text-muted" t-if="record.website_id.value" groups="website.group_multi_website">
                                    <i class="fa fa-globe me-1" title="Website"/>
                                    <field name="website_id"/>
                                </div>
                            </strong>
                            <strong class="col-4 text-end">
                                <span t-esc="record.blog_id.value"/>
                            </strong>
                            <div class="col-8">
                                <i class="fa fa-clock-o me-1" role="img" aria-label="Post date" title="Post date"/><span t-esc="record.post_date.value"/>
                            </div>
                            <div class="col-4 text-end">
                                <img t-if="record.author_id.raw_value"
                                     t-att-title="record.author_id.value"
                                     t-att-alt="record.author_id.value"
                                     class="oe_kanban_avatar o_avatar rounded"
                                     t-att-src="kanban_image('res.partner', 'avatar_128', record.author_id.raw_value)"/>
                            </div>
                        </div>
                        <div class="border-top mt-2 pt-2">
                            <field name="is_published" widget="boolean_toggle"/>
                            <t t-if="record.is_published.raw_value">Published</t>
                            <t t-else="">Not Published</t>
                        </div>
                    </div>
                </t>
            </templates>
        </kanban>
    </field>
</record>

<record id="view_blog_post_search" model="ir.ui.view">
    <field name="name">blog.post.search</field>
    <field name="model">blog.post</field>
    <field name="arch" type="xml">
        <search string="Blog Post">
            <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
            <field name="name" string="Content" filter_domain="['|', ('name','ilike',self), ('content','ilike',self)]"/>
            <field name="write_uid"/>
            <field name="blog_id"/>
            <group expand="0" string="Group By">
                <filter string="Blog" name="group_by_blog" domain="[]" context="{'group_by': 'blog_id'}"/>
                <filter string="Author" name="group_by_author" domain="[]" context="{'group_by': 'create_uid'}"/>
                <filter string="Last Contributor" name="last_contributor" domain="[]" context="{'group_by': 'write_uid'}"/>
            </group>
        </search>
    </field>
</record>

<record id="view_blog_post_list" model="ir.ui.view">
    <field name="name">Blog Post Pages Tree</field>
    <field name="model">blog.post</field>
    <field name="priority">99</field>
    <field name="arch" type="xml">
        <tree js_class="website_pages_list" type="object" action="open_website_url" multi_edit="1">
            <field name="active" column_invisible="True"/>
            <field name="name"/>
            <field name="website_url"/>

            <field name="author_id" optional="show"/>
            <field name="blog_id" optional="hide"/>
            <field name="create_uid" optional="hide"/>
            <field name="write_uid" optional="hide"/>
            <field name="write_date" optional="hide"/>

            <field name="is_seo_optimized"/>
            <field name="is_published"/>

            <field name="website_id" groups="website.group_multi_website"/>
        </tree>
    </field>
</record>

<record id="action_blog_post" model="ir.actions.act_window">
    <field name="name">Blog Post Pages</field>
    <field name="res_model">blog.post</field>
    <field name="view_mode">tree,kanban,form</field>
    <field name="view_ids" eval="[(5, 0, 0),
        (0, 0, {'view_mode': 'tree', 'view_id': ref('view_blog_post_list')}),
        (0, 0, {'view_mode': 'kanban', 'view_id': ref('blog_post_view_kanban')}),
    ]"/>
    <field name="search_view_id" ref="view_blog_post_search"/>
    <field name="context">{'create_action': 'website_blog.blog_post_action_add'}</field>
</record>

<menuitem id="menu_blog_post_pages"
    parent="website.menu_content"
    sequence="20"
    name="Blog Posts"
    action="action_blog_post"/>

</odoo>
