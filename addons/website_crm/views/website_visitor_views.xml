<?xml version="1.0" encoding="utf-8"?>
<odoo><data>
    <record id="website_visitor_view_form" model="ir.ui.view">
        <field name="name">website.visitor.view.form.inherit.website.crm</field>
        <field name="model">website.visitor</field>
        <field name="inherit_id" ref="website.website_visitor_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@id='w_visitor_visit_counter']" position="before">
                <button name="%(website_crm.crm_lead_action_from_visitor)d" type="action" class="oe_stat_button" icon="fa-star"
                        groups="sales_team.group_sale_salesman"
                        invisible="lead_count == 0">
                    <field name="lead_count" widget="statinfo" string="Leads"/>
                </button>
            </xpath>
        </field>
    </record>

    <record id="website_visitor_view_tree" model="ir.ui.view">
        <field name="name">website.visitor.view.tree.inherit.website.crm</field>
        <field name="model">website.visitor</field>
        <field name="inherit_id" ref="website.website_visitor_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='page_ids']" position="after">
                <field name="lead_count"/>
            </xpath>
        </field>
    </record>

    <record id="website_visitor_view_search" model="ir.ui.view">
        <field name="name">website.visitor.view.search.inherit.website.crm</field>
        <field name="model">website.visitor</field>
        <field name="inherit_id" ref="website.website_visitor_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='filter_type_visitor']" position="attributes">
                <attribute name="domain">[('partner_id', '=', False), ('lead_ids', '=', False)]</attribute>
            </xpath>
            <xpath expr="//filter[@name='filter_type_visitor']" position="after">
                <filter string="Leads" name="filter_type_lead" domain="[('partner_id', '=', False), ('lead_ids', '!=', False)]"/>
            </xpath>
        </field>
    </record>

    <record id="website_visitor_view_kanban" model="ir.ui.view">
        <field name="name">website.visitor.view.kanban.inherit.website.crm</field>
        <field name="model">website.visitor</field>
        <field name="inherit_id" ref="website.website_visitor_view_kanban"/>
        <field name="arch" type="xml">
            <field name="page_ids" position="after">
                <field name="lead_count"/>
            </field>
            <xpath expr="//div[@id='o_page_count']" position="after">
                <div t-if="record.lead_count.raw_value" groups="sales_team.group_sale_salesman">
                    Leads / Opportunities
                    <span class="float-end fw-bold">
                        <field name="lead_count"/>
                    </span>
                </div>
            </xpath>
            <xpath expr="//div[@id='wvisitor_visited_page']" position="after">
                <div class="col-lg col-sm-4 col-6 py-0 my-2" groups="sales_team.group_sale_salesman">
                    <span t-att-class="record.lead_count.raw_value ? 'fw-bold' : 'text-muted'">
                        <field name="lead_count"/>
                    </span>
                    <div t-att-class="record.lead_count.raw_value ? '' : 'text-muted'">Leads / Opportunities</div>
                </div>
            </xpath>
        </field>
    </record>
</data></odoo>
