# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "登録数"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "」。 次の結果を表示しています「"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr "(参照:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "(only"
msgstr "(以下のみ:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
")\n"
"                                    <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"
msgstr ""
")\n"
"                                    <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr "イベントタイトル下にあるこのスニペットを<b>ドラッグ＆ドロップ</b> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>終了</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>開始</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr "<em>参加者の言葉をここに書きましょう。イベントに自信を与えてくれます。</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "<i class=\"fa fa-ban me-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban me-2\"/>完売"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>未公開"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-calendar me-2\"/>Add to Calendar"
msgstr "<i class=\"fa fa-calendar me-2\"/>カレンダに追加"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check me-2\"/>Registered"
msgstr "<i class=\"fa fa-check me-2\"/>登録済"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid ""
"<i class=\"fa fa-chevron-left me-2\"/>\n"
"            <span>Back to events</span>"
msgstr ""
"<i class=\"fa fa-chevron-left me-2\"/>\n"
"            <span>イベントに戻る</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> グーグルカレンダーに追加"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> iCal/Outlook に追加"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>チケットを設定</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"ウェブサイト\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-map-marker fa-fw\" role=\"img\"/>Get the direction"
msgstr "<i class=\"fa fa-map-marker fa-fw\" role=\"img\"/>ルート"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge text-bg-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""
"<span class=\"badge text-bg-secondary text-uppercase "
"o_wevent_badge\">スピーカー</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<span class=\"fa fa-plus me-1\"/> Create an Event"
msgstr "<span class=\"fa fa-plus me-1\"/> イベントを作成"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span "
"class=\"o_countdown_metric pe-1\">days</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span "
"class=\"o_countdown_metric pe-1\">日</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                        <i class=\"fa fa-ban me-2\"/>Sold Out\n"
"                                    </span>"
msgstr ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                        <i class=\"fa fa-ban me-2\"/>完売\n"
"                                    </span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>オンラインイベント</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr "<strong>空席以上のチケットを注文されました</strong>"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_2
msgid "A friend"
msgstr "友人"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "過去のイベント"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "About Us"
msgstr "会社概要"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "会社概要"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "すべての国"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "All Events"
msgstr "全てのイベント"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "全ての国"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_1
msgid "Allergies"
msgstr "アレルギー"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr "ウェブサイト上でイベント固有のメニューを表示・管理できます。"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__name
msgid "Answer"
msgstr "回答"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr "回答内訳"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Answers"
msgstr "回答"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__once_per_order
msgid "Ask once per order"
msgstr "オーダごとに１回のみ質問"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"わずか13歳にして、ジョン・ドーはすでに顧客向けの最初のビジネス・アプリケーションを開発し始めていました。土木工学を学んだ後、TinyERPを設立。これは、後に世界で最もインストールされるオープンソースのビジネスソフトウェアであるOdooとなるOpenERPの初期段階でした。"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "参加者回答"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_choice_ids
msgid "Attendee Selection Answers"
msgstr "参加者選択回答"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "出席者"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "著作者"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_1
#: model:event.question.answer,name:website_event.event_5_question_0_answer_1
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_1
msgid "Blog Post"
msgstr "ブログ投稿"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "予約者"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Business Workshops"
msgstr "ビジネスワークショップ"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "公開可"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel"
msgstr "取消"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Card design"
msgstr "カードデザイン"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "閉じる"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "Come back once you have registrations to overview answers."
msgstr "登録が完了したら、戻って来て回答を確認して下さい。"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_1
msgid "Commercials"
msgstr "コマーシャル"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
#, python-format
msgid "Community"
msgstr "コミュニティ"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "コミュニティメニュー"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__company_name
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "会社"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Conference For Architects"
msgstr "建築家向けコンフェランス"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid "Configure event tickets"
msgstr "イベントチケットの作成"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Confirm Registration"
msgstr "登録を確定する"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_0
msgid "Consumers"
msgstr "消費者"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Countries"
msgstr "国"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "カバー性質"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Date"
msgstr "日付"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "日時"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "日付（新→旧）"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "日付（旧→新）"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "説明"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "ウェブサイトに専用メニューを表示"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "ウェブサイトでコミュニティタブを表示"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Don't forget to click <b>save</b> when you're done."
msgstr "完了したら <b>保存</b>を忘れずにクリックして下さい。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Download All Tickets"
msgstr "全てのチケットをダウンロード"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Download Tickets <i class=\"ms-1 fa fa-download\"/>"
msgstr "チケットをダウンロード<i class=\"ms-1 fa fa-download\"/>"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__email
#, python-format
msgid "Email"
msgstr "メール"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "終了 -"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Error"
msgstr "エラー"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__event_id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "イベント"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "イベント コミュニティメニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "イベント日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "イベント会場"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "イベントメニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Event Name"
msgstr "イベント名"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Event Page"
msgstr "イベントページ"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_pages_list
msgid "Event Pages"
msgstr "イベントページ"

#. module: website_event
#: model:ir.model,name:website_event.model_event_question
msgid "Event Question"
msgstr "イベント質問"

#. module: website_event
#: model:ir.model,name:website_event.model_event_question_answer
msgid "Event Question Answer"
msgstr "イベント質問回答"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "イベント登録"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "イベント登録回答"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "イベント登録"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Subtitle"
msgstr "イベント副題"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag
msgid "Event Tag"
msgstr "イベントタグ"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "イベントタグカテゴリ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_dynamic_snippet_options_template
msgid "Event Tags"
msgstr "イベントタグ"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "イベントテンプレート"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Title"
msgstr "イベントタイトル"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_type_id
msgid "Event Type"
msgstr "イベントタイプ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "イベントが見つかりません！"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "イベントの公開"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "イベントの非公開化"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#: model:ir.ui.menu,name:website_event.menu_event_pages
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.snippets
#, python-format
msgid "Events"
msgstr "イベント"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Events Page"
msgstr "イベントページ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Expired"
msgstr "契約期間終了済"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr "追加登録ボタン"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Filter by Country"
msgstr "国ごとのフィルタ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
msgid "Filter by Date"
msgstr "日にちごとにフィルタ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "Filters"
msgstr "フィルタ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr "このイベントに関する人々の意見や感想を聞いて、会話に参加しましょう。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Follow Us"
msgstr "よかったらフォローお願いします"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "以下のコンテンツは全てのイベントに表示されます。"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__general_question_ids
msgid "General Questions"
msgstr "一般的な質問"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "Google"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Great Reno Ballon Race"
msgstr "Great Reno気球レース"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Grid"
msgstr "グリッド"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Hockey Tournament"
msgstr "ホッケートーナメント"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_1
msgid "How did you hear about us?"
msgstr "当社についてどのように知りましたか？"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_2
#: model:event.question,title:website_event.event_1_question_0
#: model:event.question,title:website_event.event_5_question_0
#: model:event.question,title:website_event.event_type_data_sports_question_0
msgid "How did you learn about this event?"
msgstr "このイベントについてどのようにして知りましたか？"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__id
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "ID"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"チェックを付けた場合、この質問は一度だけ行われ、その値はすべての出席者に伝わります。そうでない場合は、予約の参加者全員に対して質問されます。"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
#, python-format
msgid "Introduction"
msgstr "概要"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "紹介メニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "紹介メニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "完了"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "現在進行中"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "参加予定"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "公開済"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "ジョン・ドー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Layout"
msgstr "レイアウト"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "List"
msgstr "リスト"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Live Music Festival"
msgstr "ライブ音楽祭"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr "ロケーション"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "ロケーションメニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "ロケーションメニュー"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr "素晴らしい！では、このページを<b>公開</b>し、ウェブサイトで<b>閲覧可能</b>にしましょう！"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Mandatory"
msgstr "必須"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr "必須回答"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_0
msgid "Meal Type"
msgstr "食事タイプ"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "メニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "メニュータイプ"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "メニュー"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_0
msgid "Mixed"
msgstr "混合"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields,field_description:website_event.field_event_event__address_name
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__name
#, python-format
msgid "Name"
msgstr "名称"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_event_action_add
msgid "New Event"
msgstr "新しいイベント"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#, python-format
msgid "Next Events"
msgstr "次のイベント"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "No Answers yet!"
msgstr "回答がまだありません！"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "ウェブサイトメニュー項目がまだありません！"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No event matching your search criteria could be found."
msgstr "検索条件に一致するイベントが見つかりませんでした。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events scheduled yet"
msgstr "イベントがまだ予定されていません"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "この訪問者にリンクする登録がありません"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "次の検索結果が見つかりませんでした: 「"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Not Published"
msgstr "未公開"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Once per Order"
msgstr "オーダごとに1回"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Online Events"
msgstr "オンラインイベント"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "OpenWood Collection Online Reveal"
msgstr "OpenWoodコレクションオンライン公開"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "オーガナイザ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "当社のトレーニング"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_0
msgid "Our website"
msgstr "当社のウェブサイト"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Past Events"
msgstr "過去のイベント"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr "パスタファリアン(麺好きの人々)"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__phone
#, python-format
msgid "Phone"
msgstr "電話"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Photos"
msgstr "写真"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Published"
msgstr "公開済"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Question"
msgstr "質問"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "質問の種類"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid "Question cannot be linked to both an Event and an Event Type."
msgstr "質問は、イベントとイベントタイプの両方にリンクすることはできません。"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Questions"
msgstr "質問"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Quotes"
msgstr "引用"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_2
#: model:event.question.answer,name:website_event.event_5_question_0_answer_2
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_2
msgid "Radio Ad"
msgstr "ラジオ広告"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr "参照:"

#. module: website_event
#. odoo-javascript
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.navbar
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Register"
msgstr "登録"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr "登録ボタン"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "メニュー登録"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "メニュー登録"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "イベント登録"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__registration_id
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#, python-format
msgid "Registration"
msgstr "登録"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "登録が確認されました！"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registration failed! These tickets are not available anymore."
msgstr "登録に失敗しました！このチケットはもう入手できません。"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "参加者"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr "登録を締め切りました"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr "登録を <b>締切ました</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr "登録をまだ受け付けていません"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "開始前の残り時間"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "開始前の残り時間(分)"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_2
msgid "Research"
msgstr "研究"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
#: model:ir.model.fields,help:website_event.field_event_tag__website_id
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_id
msgid "Restrict publishing to this website."
msgstr "このウェブサイトへの公開を制限。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "イベントリストに戻る。"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO最適化済"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_1
msgid "Sales"
msgstr "販売"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales end on"
msgstr "販売終了日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales start on"
msgstr "販売開始"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_card
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_picture
msgid "Sample"
msgstr "サンプル"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "イベントを検索..."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_tree
msgid "Selected Answers"
msgstr "選択された回答"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "選択された回答"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "選択"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "SEO名"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Share"
msgstr "共有"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "ウェブサイトに掲載"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sidebar"
msgstr "サイドバー"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_0
#: model:event.question.answer,name:website_event.event_5_question_0_answer_0
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_0
msgid "Social Media"
msgstr "ソーシャルメディア"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "完売"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "申し訳ありません。このイベントはもう利用できません。"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "特定の質問"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "開始 -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "本日開始"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Start → End"
msgstr "開始 → 終了"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "開始<span/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Stats"
msgstr "統計"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sub-menu (Specific)"
msgstr "サブ・メニュー (特定)"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "推奨された答え"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptchaが疑わしい活動を検知しました。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Template badge"
msgstr "テンプレートバッジ"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "テキスト入力"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "テキスト解答"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "サイト経由して、文書にアクセスする完全なURL。"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "The website must be from the same company as the event."
msgstr "ウェブサイトはイベントと同じ会社のものである必要があります。"

#. module: website_event
#: model:ir.model.constraint,message:website_event.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "推奨値またはテキスト値が必要です。"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "This month"
msgstr "今月"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This shortcut will bring you right back to the event form."
msgstr "このショートカットを使うと、イベントフォームにすぐに戻ることができます。"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr "この技術メニューには、すべてのイベントサブ・メニュー項目が表示されます。"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This ticket is not available for sale for this event"
msgstr "このチケットはこのイベントでは販売していません。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "チケット数"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr "チケット販売開始:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets"
msgstr "チケット"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr "このイベントのチケットは <b>完売しました</b>"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__title
msgid "Title"
msgstr "タイトル"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Today"
msgstr "今日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid "Toggle navigation"
msgstr "ナビゲーションを切り替える"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Top Bar Filter"
msgstr "トップバーフィルタ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
msgid "Type"
msgstr "タイプ"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:website.snippet.filter,name:website_event.website_snippet_filter_event_list
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Upcoming Events"
msgstr "今後のイベント予定"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr "この<b>ショートカット</b>を使ってイベントウェブページに簡単にアクセスできます。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragraph to write a short text about your events or company."
msgstr "この段落を使い、イベントや会社についての短い文章を書いて下さい。"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "URLベースのメニューでない場合に使用されます。"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr "ベジタリアン"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "ビュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "現在のサイトに表示"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "訪問者"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event matching your search for:"
msgstr "以下の検索に一致するイベントが見つかりませんでした:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event scheduled at this moment."
msgstr "現在、予定されているイベントが見つかりませんでした。"

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_id
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
msgid "Website"
msgstr "ウェブサイト"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "ウェブサイトイベントメニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "ウェブサイトイベントメニュー"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "ウェブサイトホーム"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "ウェブサイトメニュー"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "ウェブサイトメニュー"

#. module: website_event
#: model:ir.model,name:website_event.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "サイトスニペットフィルタ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "ウェブサイト サブメニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "サイトURL"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "サイト訪問者"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "サイトメタディスクリプション"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "サイトメタキーワード"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "サイトメタタイトル"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "サイトopengraph画像"

#. module: website_event
#: model:event.question,title:website_event.event_5_question_1
msgid "What's your Hockey level?"
msgstr "あなたのホッケーレベルは何ですか？"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "イベントが開始したか"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "現在進行中でない場合、イベントが本日開始か"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_0
msgid "Which field are you working in"
msgstr "どの分野で働いていますか？"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr "編集ボタンを使用すると、訪問者が登録時に表示されるウェブページを<b>カスタマイズ</b>することができます。"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr "すでに回答がある質問のタイプを変更することはできません！"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid ""
"You cannot delete a question that has already been answered by attendees."
msgstr "すでに参加者が回答した質問を削除することはできません。"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question_answer.py:0
#, python-format
msgid ""
"You cannot delete an answer that has already been selected by attendees."
msgstr "すでに参加者が選択した質問を削除することはできません。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "available)"
msgstr "利用可能)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "e.g. \"Conference for Architects\""
msgstr "例：“建築家向けカンファレンス”"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr "例. \"食事制限はありますか？\""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr "iCal/Outlook"
