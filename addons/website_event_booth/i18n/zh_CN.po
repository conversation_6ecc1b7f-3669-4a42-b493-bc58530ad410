# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth
# 
# Translators:
# Wil Odoo, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<i class=\"fa fa-exclamation-triangle me-2\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                    <span class=\"o_wbooth_registration_error_message\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-2\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                <span class=\"o_wbooth_registration_error_message\"/>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                        <span>Sorry, several booths are now sold out. Please change your choices before validating again.</span>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                        <span>很抱歉，有几个展位已经售罄。请更改您的选择后再重新验证。</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<i class=\"fa fa-map-o me-2\"/>View Plan"
msgstr "<i class=\"fa fa-map-o me-2\"/>查看平面图"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "<span class=\"fa fa-gear me-1\"/> Configure Booths"
msgstr "<span class=\"fa fa-gear me-1\"/> 设置展位"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span class=\"text-nowrap\">Sold Out</span>"
msgstr "<span class=\"text-nowrap\">售罄</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span>Book my Booth(s)</span>"
msgstr "<span>预订我的展位</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "<span>Book my Booths</span>"
msgstr "<span>预订我的展位</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid ""
"<span>Booth Selection</span><span class=\"fa fa-angle-right d-inline-block "
"align-middle mx-sm-3 text-muted fs-5\"/>"
msgstr ""
"<span>展位选择</span><span class=\"fa fa-angle-right d-inline-block align-middle"
" mx-sm-3 text-muted fs-5\"/>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid "<span>Confirmed</span>"
msgstr "<span>已确认</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>Email</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>名称</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_type_view_form
msgid "Booth Menu Item"
msgstr "展位菜单项"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu
msgid "Booth Register"
msgstr "展位登记"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/xml/event_booth_registration_templates.xml:0
#, python-format
msgid "Booth Registration completed!"
msgstr "展位注册完成！"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "Booth registration failed."
msgstr "展位注册失败。"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_type__booth_menu
msgid "Booths on Website"
msgstr "网站上的展位"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Check our"
msgstr "请看看我们的"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Choose your type of booth"
msgstr "选择展位类型"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Contact Details"
msgstr "联系人细节"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid ""
"Contact Details<span class=\"fa fa-angle-right d-inline-block align-middle "
"mx-sm-3 text-muted fs-5\"/>"
msgstr ""
"联系方式<span class=\"fa fa-angle-right d-inline-block align-middle mx-sm-3 "
"text-muted fs-5\"/>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Contact Us"
msgstr "联系我们"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_event
msgid "Event"
msgstr "活动"

#. module: website_event_booth
#: model:ir.model.fields.selection,name:website_event_booth.selection__website_event_menu__menu_type__booth
msgid "Event Booth Menus"
msgstr "活动展位菜单"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu_ids
msgid "Event Booths Menus"
msgstr "活动展位菜单"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Event Finished"
msgstr "活动已结束"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_type
msgid "Event Template"
msgstr "活动模板"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__exhibition_map
msgid "Exhibition Map"
msgstr "展览地图"

#. module: website_event_booth
#. odoo-python
#: code:addons/website_event_booth/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
#, python-format
msgid "Get A Booth"
msgstr "获得展位"

#. module: website_event_booth
#. odoo-javascript
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "It looks like your email is linked to an existing account."
msgstr "您的电子邮件似乎已与现有账户关联。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Link to list of future events"
msgstr "未来活动列表链接"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "List of Future Events"
msgstr "未来活动列表"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Location"
msgstr "位置"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "菜单类型"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Phone"
msgstr "电话"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Please Sign In."
msgstr "请登入。"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "Please fill out the form correctly."
msgstr "请正确填写表格。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Registration Not Open."
msgstr "未开放注册。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Sorry, all the booths are sold out."
msgstr "对不起，所有的摊位都卖完了。"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "The booth category doesn't exist."
msgstr "展位类别不存在。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "This event is not open to exhibitors registration at this time."
msgstr "本次活动暂不接受参展商报名。"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_website_event_menu
msgid "Website Event Menu"
msgstr "网站活动菜单"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "if you have any question."
msgstr "如果您有任何问题。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "list of future events"
msgstr "将来活动列表"
