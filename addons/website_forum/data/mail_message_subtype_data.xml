<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Answers subtypes -->
        <record id="mt_answer_new" model="mail.message.subtype">
            <field name="name">New Answer</field>
            <field name="res_model">forum.post</field>
            <field name="default" eval="True"/>
            <field name="hidden" eval="False"/>
            <field name="description">New Answer</field>
        </record>
        <record id="mt_answer_edit" model="mail.message.subtype">
            <field name="name">Answer Edited</field>
            <field name="res_model">forum.post</field>
            <field name="default" eval="False"/>
            <field name="description">Answer Edited</field>
        </record>
        <!-- Questions subtypes -->
        <record id="mt_question_new" model="mail.message.subtype">
            <field name="name">New Question</field>
            <field name="res_model">forum.post</field>
            <field name="default" eval="True"/>
            <field name="description">New Question</field>
        </record>
        <record id="mt_question_edit" model="mail.message.subtype">
            <field name="name">Question Edited</field>
            <field name="res_model">forum.post</field>
            <field name="default" eval="False"/>
            <field name="description">Question Edited</field>
        </record>
        <!-- Forum subtypes, to follow all answers or questions -->
        <record id="mt_forum_answer_new" model="mail.message.subtype">
            <field name="name">New Answer</field>
            <field name="res_model">forum.forum</field>
            <field name="default" eval="True"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" ref="mt_answer_new"/>
            <field name="relation_field">forum_id</field>
        </record>
        <record id="mt_forum_question_new" model="mail.message.subtype">
            <field name="name">New Question</field>
            <field name="res_model">forum.forum</field>
            <field name="default" eval="True"/>
            <field name="hidden" eval="False"/>
            <field name="parent_id" ref="mt_question_new"/>
            <field name="relation_field">forum_id</field>
        </record>
    </data>
</odoo>
