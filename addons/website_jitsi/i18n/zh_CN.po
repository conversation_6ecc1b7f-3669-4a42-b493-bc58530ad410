# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_jitsi
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__12
msgid "12"
msgstr "12"

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__16
msgid "16"
msgstr "16"

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__20
msgid "20"
msgstr "20"

#. module: website_jitsi
#: model:ir.model,name:website_jitsi.model_chat_room
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__chat_room_id
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_form
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_search
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_tree
msgid "Chat Room"
msgstr "聊天室"

#. module: website_jitsi
#: model:ir.model,name:website_jitsi.model_chat_room_mixin
msgid "Chat Room Mixin"
msgstr "混合聊天室"

#. module: website_jitsi
#: model:ir.actions.act_window,name:website_jitsi.chat_room_action
#: model:ir.ui.menu,name:website_jitsi.chat_room_menu
msgid "Chat Rooms"
msgstr "聊天室"

#. module: website_jitsi
#. odoo-javascript
#: code:addons/website_jitsi/static/src/xml/chat_room_modal.xml:0
#, python-format
msgid "Close"
msgstr "关闭"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__create_uid
msgid "Created by"
msgstr "创建人"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__create_date
msgid "Created on"
msgstr "创建日期"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__is_full
msgid "Full"
msgstr "全"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__id
msgid "ID"
msgstr "ID"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__jitsi_server_domain
msgid "Jitsi Server Domain"
msgstr "Jitsi Server 域名"

#. module: website_jitsi
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_join_button
msgid "Join the room"
msgstr "加入房间"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__lang_id
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_lang_id
msgid "Language"
msgstr "语言"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__last_activity
msgid "Last Activity"
msgstr "上个活动"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_last_activity
msgid "Last activity"
msgstr "上次活动"

#. module: website_jitsi
#. odoo-javascript
#: code:addons/website_jitsi/static/src/xml/chat_room_modal.xml:0
#, python-format
msgid "Loading your room..."
msgstr "正在加载您的房间..."

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__max_capacity
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_max_capacity
msgid "Max capacity"
msgstr "最大能力"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__max_participant_reached
msgid "Max participant reached"
msgstr "已达到最大参与者"

#. module: website_jitsi
#: model:ir.model.fields,help:website_jitsi.field_chat_room__max_participant_reached
#: model:ir.model.fields,help:website_jitsi.field_chat_room_mixin__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "房间内同时达到的最大参与者人数"

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__no_limit
msgid "No limit"
msgstr "没有限制"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__participant_count
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_participant_count
msgid "Participant count"
msgstr "参加人数"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_max_participant_reached
msgid "Peak participants"
msgstr "高峰参与者"

#. module: website_jitsi
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_form
msgid "Reporting"
msgstr "报告"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_is_full
msgid "Room Is Full"
msgstr "房间满了"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__name
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_name
msgid "Room Name"
msgstr "房间名称"

#. module: website_jitsi
#: model:ir.model.fields,help:website_jitsi.field_chat_room__jitsi_server_domain
msgid ""
"The Jitsi server domain can be customized through the settings to use a "
"different server than the default \"meet.jit.si\""
msgstr "可以通过设置自定义 Jitsi 服务器域以使用与默认“meet.jit.si”不同的服务器"
