# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_livechat
# 
# Translators:
# Wil Odoo, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__session_count
msgid "# Sessions"
msgstr "# 会话"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/discuss_channel.py:0
#, python-format
msgid ""
"%s started a conversation with %s.\n"
"                        The chat request has been canceled."
msgstr ""
"%s已开始与%s对话。\n"
"                        聊天请求已被取消。"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<small>%</small>"
msgstr "<small>%</small>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<span>Livechat Channel</span>"
msgstr "<span>聊天频道</span>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "Available"
msgstr "可用"

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/web/thread_patch.xml:0
#, python-format
msgid "Avatar"
msgstr "形象"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Bad"
msgstr "糟糕"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__can_publish
msgid "Can Publish"
msgstr "可以发布"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Channel"
msgstr "频道"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.im_livechat_channel_view_form_add
msgid "Channel Name"
msgstr "渠道名称"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_tree
msgid "Chat"
msgstr "聊天"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "聊天机器人脚本"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "聊天机器人脚本步骤"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Chats"
msgstr "聊天"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_description
msgid "Description of the channel displayed on the website page"
msgstr "渠道的说明显示在网站上"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr "讨论频道"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Great"
msgstr "很棒"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Happy face"
msgstr "笑脸"

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/web/thread_patch.xml:0
#, python-format
msgid "History"
msgstr "历史"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "In Conversation"
msgstr "在谈话中"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__is_published
msgid "Is Published"
msgstr "已发布"

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/web/thread_patch.xml:0
#, python-format
msgid "Lang"
msgstr "语言"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/website.py:0
#, python-format
msgid "Live Support"
msgstr "即时支持"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_im_livechat_channel
msgid "Livechat Channel"
msgstr "即时聊天"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "Livechat Support Channels"
msgstr "即时聊天支持渠道"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Neutral face"
msgstr "平静"

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.im_livechat_channel_action_add
msgid "New Channel"
msgstr "新频道"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid "No Livechat Channel allows you to send a chat request for website %s."
msgstr "没有 在线聊天频道 允许您发送网站聊天请求%s."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Not rated yet"
msgstr "欢迎咨询"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Okay"
msgstr "OK"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Operator Avatar"
msgstr "操作员形象"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_name
msgid "Operator Name"
msgstr "操作员名称"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid ""
"Recipients are not available. Please refresh the page to get latest visitors"
" status."
msgstr "收件人不可用。请刷新网页以获取最新的访问者状态。"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Sad face"
msgstr "悲伤的表情"

#. module: website_livechat
#: model:ir.actions.server,name:website_livechat.website_livechat_send_chat_request_action_server
msgid "Send Chat Requests"
msgstr "发送聊天请求"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
msgid "Send chat request"
msgstr "发送聊天请求"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Speaking With"
msgstr "语音"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_id
msgid "Speaking with"
msgstr "语音"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Statistics"
msgstr "统计信息"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.chatbot_script_view_form
msgid "Test"
msgstr "测试"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The"
msgstr "此"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The Team"
msgstr "团队"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_url
msgid "The full URL to access the document through the website."
msgstr "通过网站访问文档的完整网址。"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/discuss_channel.py:0
#, python-format
msgid "The visitor"
msgstr "访客"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "There are no public livechat channels to show."
msgstr "没有可显示的公共实时聊天频道。"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "There are no ratings for this channel for now."
msgstr "欢迎对我们的服务进行评价"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_published
msgid "Visible on current website"
msgstr "在当前网站显示"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_discuss_channel__livechat_visitor_id
msgid "Visitor"
msgstr "访问者"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/controllers/main.py:0
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid "Visitor #%d"
msgstr "访客 #%d"

#. module: website_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/website_livechat/controllers/main.py:0
#: code:addons/website_livechat/static/src/web/persona_model_patch.js:0
#, python-format
msgid "Visitor #%s"
msgstr "访客 #%s"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/discuss_channel.py:0
#, python-format
msgid "Visitor %s left the conversation."
msgstr "访客 %s 离开了对话。"

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.website_visitor_livechat_session_action
msgid "Visitor's Sessions"
msgstr "访客会话"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__discuss_channel_ids
msgid "Visitor's livechat channels"
msgstr "访客的在线聊天频道"

#. module: website_livechat
#: model:ir.ui.menu,name:website_livechat.website_livechat_visitor_menu
msgid "Visitors"
msgstr "访问者"

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/web/thread_patch.xml:0
#: model:ir.model,name:website_livechat.model_website
#, python-format
msgid "Website"
msgstr "网站"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_res_config_settings__channel_id
msgid "Website Live Channel"
msgstr "网站直播频道"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website__channel_id
msgid "Website Live Chat Channel"
msgstr "网站在线客服"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_url
msgid "Website URL"
msgstr "网站网址"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/tests/test_livechat_basic_flow.py:0
#: model:ir.model,name:website_livechat.model_website_visitor
#, python-format
msgid "Website Visitor"
msgstr "网页访问者"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_description
msgid "Website description"
msgstr "网站说明"

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/discuss_channel.py:0
#, python-format
msgid "an operator"
msgstr "一个操作员"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "last feedbacks"
msgstr "最后反馈"
