# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_wishlist
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-heart\"/>\n"
"            Wishlist"
msgstr ""
"<i class=\"fa fa-fw fa-heart\"/>\n"
"            ウィッシュリスト"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid ""
"<i class=\"fa fa-heart-o me-2\" role=\"img\" aria-label=\"Add to "
"wishlist\"/>Add to wishlist"
msgstr ""
"<i class=\"fa fa-heart-o me-2\" role=\"img\" aria-label=\"Add to "
"wishlist\"/>ウィッシュリストに追加"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "<small><i class=\"fa fa-trash-o\"/> Remove</small>"
msgstr "<small><i class=\"fa fa-trash-o\"/> 削除</small>"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid ""
"<span class=\"fa fa-fw fa-shopping-cart\"/>\n"
"                                                <span class=\"d-none d-md-inline\">Add</span>"
msgstr ""
"<span class=\"fa fa-fw fa-shopping-cart\"/>\n"
"                                                <span class=\"d-none d-md-inline\">追加</span>"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
msgid "<span class=\"fa fa-heart\" role=\"img\" aria-label=\"Add to wishlist\"/>"
msgstr "<span class=\"fa fa-heart\" role=\"img\" aria-label=\"Add to wishlist\"/>"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__active
msgid "Active"
msgstr "有効化"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add product to my cart but keep it in my wishlist"
msgstr "商品をカートに追加し、ウィッシュリストにも残す"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
msgid "Add to Wishlist"
msgstr "ウィッシュリストに追加"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid "Add to wishlist"
msgstr "ウィッシュリストに追加"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Contact Us"
msgstr "お問い合わせ"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__currency_id
msgid "Default Currency"
msgstr "デフォルト通貨"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_sale_wishlist
#: model:ir.model.constraint,message:website_sale_wishlist.constraint_product_wishlist_product_unique_partner_id
msgid "Duplicated wishlisted product for this partner."
msgstr "この取引先のウィッシュリストに重複登録されている商品"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__id
msgid "ID"
msgstr "ID"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "My Wishlist"
msgstr "自分のウィッシュリスト"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__partner_id
msgid "Owner"
msgstr "オーナー"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__price
msgid "Price"
msgstr "価格"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__price
msgid "Price of the product when it has been added in the wishlist"
msgstr "ウィッシュリストへの追加時のプロダクト価格"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist"
msgstr "価格表"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist when added"
msgstr "追加時の価格リスト"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_template
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__product_id
msgid "Product"
msgstr "プロダクト"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_product
msgid "Product Variant"
msgstr "プロダクトバリアント"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "商品ウィッシュリスト"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Product image"
msgstr "プロダクト画像"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Shop Wishlist"
msgstr "ショップウィッシュリスト"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Show/hide empty wishlist"
msgstr "空のウィッシュリストを表示/隠す"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__website_id
msgid "Website"
msgstr "ウェブサイト"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_partner__wishlist_ids
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_users__wishlist_ids
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.header_wishlist_link
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Wishlist"
msgstr "ウィッシュリスト"
