# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_twitter
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:50+0000\n"
"PO-Revision-Date: 2015-09-08 11:02+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Galician (http://www.transifex.com/odoo/odoo-9/language/gl/)\n"
"Language: gl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ", <strong>create a project</strong> with the following information:"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ", click on <strong>Elevated</strong> then on <strong>Apply</strong> and finally complete the form."
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Show me how to obtain the Twitter API key and Twitter API secret"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>App Name: </strong> choose a unique name"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Description: </strong> Odoo Twitter Integration"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Name: </strong> Odoo Twitter Integration"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Use Case: </strong> Embedding Tweets in a website"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_key
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API Key"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_secret
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API secret"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Authentication credentials were missing or incorrect. Maybe screen name tweets are protected."
msgstr ""

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Copy/Paste the API Key and API Key Secret values into the above fields"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_date
msgid "Created on"
msgstr "Creado o"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__display_name
msgid "Display Name"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_screen_name
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Favorites From"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Get Elevated access by going to"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_screen_name
msgid "Get favorites from this screen name"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "How to configure the Twitter API access"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__id
msgid "ID"
msgstr "ID"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Internet connection refused: We failed to reach a twitter server."
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Log in or create an account on"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "On the"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Once connected, and if not already done, complete the Twitter portal access process on"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Please set a Twitter screen name to load favorites from, in the Website Settings (it does not have to be yours)"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr ""

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/js/website.twitter.editor.js:0
#, python-format
msgid "Reload"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Request cannot be served due to the applications rate limit having been exhausted for the resource."
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__screen_name
msgid "Screen Name"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_screen_name
msgid "Screen Name of the Twitter Account from which you want to load favorites.It does not have to match the API Key/Secret."
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "The Twitter servers are up, but the request could not be serviced due to some failure within our stack. Try again later."
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "The request is understood, but it has been refused or access is not allowed. Please check your Twitter API Key and Secret."
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "The request was invalid or cannot be otherwise served. Requests without authentication are considered invalid and will yield this response."
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "There was no new data to return."
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet_id
msgid "Tweet ID"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet
msgid "Tweets"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter API Credentials"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_key
msgid "Twitter API Key"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_secret
msgid "Twitter API Secret"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_key
msgid "Twitter API key"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_secret
msgid "Twitter API secret"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/"
msgstr ""

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter Configuration"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter Portal"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter Roller"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.website_twitter_snippet
msgid "Twitter Scroller"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter authorization error! Please double-check your Twitter API Key and Secret!"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter is down or being upgraded."
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter seems broken. Please retry later. You may consider posting an issue on Twitter forums to get help."
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_server_uri
msgid "Twitter server uri"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter tutorial"
msgstr ""

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Twitter user @%(username)s has less than 12 favorite tweets. Please add more or choose a different screen name."
msgstr ""

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter's user"
msgstr ""

#. module: website_twitter
#: model:ir.actions.server,name:website_twitter.ir_cron_twitter_actions_ir_actions_server
msgid "Twitter: Fetch new favorites"
msgstr ""

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Website Twitter"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/en/portal/products"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/portal/"
msgstr ""
