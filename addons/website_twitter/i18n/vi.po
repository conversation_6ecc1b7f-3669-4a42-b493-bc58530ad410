# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_twitter
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ", <strong>create a project</strong> with the following information:"
msgstr ", <strong>tạo một dự án</strong> với thông tin sau:"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
", click on <strong>Elevated</strong> then on <strong>Apply</strong> and "
"finally complete the form."
msgstr ""
", nhấp vào <strong>Nâng cao</strong> rồi nhấp vào <strong>Áp dụng</strong> "
"và cuối cùng là hoàn thành biểu mẫu."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Show me how to obtain the Twitter API key and Twitter API secret"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Chỉ cho tôi cách lấy mã khóa Twitter API và mã bí mật Twitter API"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>App Name: </strong> choose a unique name"
msgstr "<strong>Tên ứng dụng: </strong> chọn tên duy nhất"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Description: </strong> Odoo Twitter Integration"
msgstr "<strong>Mô tả: </strong> Odoo tích hợp Twitter "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Name: </strong> Odoo Twitter Integration"
msgstr "<strong>Tên: </strong> Odoo tích hợp Twitter "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Use Case: </strong> Embedding Tweets in a website"
msgstr "<strong>Trường hợp sử dụng: </strong> Chèn Tweet vào trang web"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_key
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API Key"
msgstr "Khóa API"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_secret
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API secret"
msgstr "Mã bí mật API"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""
"Xác thực thông tin đăng nhập bị thiếu hoặc không chính xác. Có thể tên màn "
"hình tweet được bảo vệ.  "

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Copy/Paste the API Key and API Key Secret values into the above fields"
msgstr ""
"Sao chép/Dán các giá trị Mã khóa API và Bí mật mã khoá API vào các trường "
"trên"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_screen_name
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Favorites From"
msgstr "Yêu thích từ"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Get Elevated access by going to"
msgstr "Nhận quyền truy cập nâng cao bằng cách truy cập"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_screen_name
msgid "Get favorites from this screen name"
msgstr "Lấy tin yêu thích từ tên màn hình này"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr "Lỗi HTTP: Định cấu hình sai "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "How to configure the Twitter API access"
msgstr "Cách định cấu hình truy cập Twitter API"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__id
msgid "ID"
msgstr "ID"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Internet connection refused: We failed to reach a twitter server."
msgstr ""
"Kết nối Internet bị từ chối: Kết nối với máy chủ twitter bị thất bại. "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Log in or create an account on"
msgstr "Đăng nhập hoặc tạo tài khoản trên"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "On the"
msgstr "Trên"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Once connected, and if not already done, complete the Twitter portal access "
"process on"
msgstr ""
"Sau khi kết nối và nếu chưa hoàn tất, hãy hoàn thành quy trình truy cập cổng"
" Twitter trên"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""
"Hãy đặt tên màn hình twitter để tải về tin yêu thích, trong cài đặt website "
"(không nhất thiết phải là tên của bạn)"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr "Hãy đặt Twitter API Key và Secret trong Cài đặt website"

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/js/website.twitter.editor.js:0
#, python-format
msgid "Reload"
msgstr "Tải lại"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr ""
"Yêu cầu không thể thực hiện do giới hạn tần số ứng dụng cho nguồn này đã "
"hết. "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__screen_name
msgid "Screen Name"
msgstr "Tên hiển thị"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""
"Tên hiển thị của tài khoản Twitter mà bạn tải về tin yêu thích. Không nhất "
"thiết phải khớp với  API Key/Secret."

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""
"Máy chủ Twitter đã cập nhật nhưng quá tải yêu cầu. Vui lòng thử lại sau. "

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""
"Máy chủ Twitter đã cập nhật nhưng yêu cầu không thể thực hiện do lỗi trong "
"cấu trúc dữ liệu stack của chúng tôi. Hãy thử lại sau. "

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed."
" Please check your Twitter API Key and Secret."
msgstr ""
"Yêu cầu được tiếp nhận nhưng bị từ chối hoặc truy cập không được phép. Vui "
"lòng kiểm tra API Key và Secret của Twitter. "

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""
"Yêu cầu không hợp lệ hoặc không thể thực hiện. Yêu cầu không có xác thực "
"được coi là không hợp lệ và sẽ dẫn đến phản hồi này. "

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "There was no new data to return."
msgstr "Không có dữ liệu mới được trả lại. "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet_id
msgid "Tweet ID"
msgstr "ID Tweet"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet
msgid "Tweets"
msgstr "Tweet"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter API Credentials"
msgstr "Thông tin xác thực API Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_key
msgid "Twitter API Key"
msgstr "API Key Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_secret
msgid "Twitter API Secret"
msgstr "API Secret Twitter"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_key
msgid "Twitter API key"
msgstr "API key Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/"
msgstr "API key Twitter bạn có thể lấy từ https://apps.twitter.com/ "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_secret
msgid "Twitter API secret"
msgstr "API secret Twitter "

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/"
msgstr "Twitter API secret bạn có thể lấy từ https://apps.twitter.com/"

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter Configuration"
msgstr "Cấu hình Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter Portal"
msgstr "Cổng thông tin Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter Roller"
msgstr "Twitter Roller"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.website_twitter_snippet
msgid "Twitter Scroller"
msgstr "Twitter Scroller"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter authorization error! Please double-check your Twitter API Key and "
"Secret!"
msgstr ""
"Lỗi xác thực Twitter! Vui lòng kiểm tra lại API Key và Secret của Twitter "
"của bạn!"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter is down or being upgraded."
msgstr "Twitter không hoạt động hoặc đang được cập nhật. "

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""
"Có vẻ Twitter bị hỏng. Vui lòng thử lại sau. Bạn có thể cân nhắc thử đăng "
"lỗi này trong diễn đàn Twitter để nhận trợ giúp. "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_server_uri
msgid "Twitter server uri"
msgstr "Uri máy chủ Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter tutorial"
msgstr "Hướng dẫn Twitter"

#. module: website_twitter
#. odoo-python
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more"
" or choose a different screen name."
msgstr ""
"Người dùng Twitter @%(username)s có ít hơn 12 tweet yêu thích. Vui lòng thêm"
" hoặc chọn một tên hiển thị khác. "

#. module: website_twitter
#. odoo-javascript
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter's user"
msgstr "Người dùng Twitter"

#. module: website_twitter
#: model:ir.actions.server,name:website_twitter.ir_cron_twitter_actions_ir_actions_server
msgid "Twitter: Fetch new favorites"
msgstr "Twitter: Nạp tin yêu thích mới"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__website_id
msgid "Website"
msgstr "Trang web"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Website Twitter"
msgstr "Website Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/"
msgstr "https://developer.twitter.com/"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/en/portal/products"
msgstr "https://developer.twitter.com/en/portal/products"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://developer.twitter.com/portal/"
msgstr "https://developer.twitter.com/portal/"
