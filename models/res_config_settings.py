# -*- coding: utf-8 -*-
# Copyright 2025 Imam <PERSON><PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    pos_iface_show_refund_button = fields.Boolean(
        related='pos_config_id.iface_show_refund_button',
        readonly=False,
        help='If enabled, the Refund button will be shown in the POS interface.'
    )
    
    pos_iface_show_cash_control = fields.Boolean(
        related='pos_config_id.iface_show_cash_control',
        readonly=False,
        help='If enabled, the Cash In/Out button will be shown in the POS interface.'
    )
