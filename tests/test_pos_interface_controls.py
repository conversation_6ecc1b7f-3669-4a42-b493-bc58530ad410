# -*- coding: utf-8 -*-
# Copyright 2025 Imam <PERSON><PERSON><PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

from odoo.tests import tagged
from odoo.addons.point_of_sale.tests.common import TestPoSCommon


@tagged('post_install', '-at_install')
class TestPosInterfaceControls(TestPoSCommon):
    """Test the POS Interface Control configuration options"""

    def setUp(self):
        super(TestPosInterfaceControls, self).setUp()
        self.config = self.basic_config

    def test_pos_config_cash_control_button(self):
        """Test that the iface_show_cash_control field works correctly"""
        # By default, the cash control button should be hidden
        self.assertFalse(self.config.iface_show_cash_control,
                         "Cash Control button should be hidden by default")

        # Enable the cash control button
        self.config.write({'iface_show_cash_control': True})
        self.assertTrue(self.config.iface_show_cash_control,
                        "Cash Control button should be shown after enabling")

        # Disable the cash control button
        self.config.write({'iface_show_cash_control': False})
        self.assertFalse(self.config.iface_show_cash_control,
                         "Cash Control button should be hidden after disabling")

    def test_pos_config_product_images_button(self):
        """Test that the iface_show_product_images field works correctly"""
        # By default, the product images button should be hidden
        self.assertFalse(self.config.iface_show_product_images,
                         "Product Images button should be hidden by default")

        # Enable the product images button
        self.config.write({'iface_show_product_images': True})
        self.assertTrue(self.config.iface_show_product_images,
                        "Product Images button should be shown after enabling")

        # Disable the product images button
        self.config.write({'iface_show_product_images': False})
        self.assertFalse(self.config.iface_show_product_images,
                         "Product Images button should be hidden after disabling")

    def test_pos_config_category_images_button(self):
        """Test that the iface_show_category_images field works correctly"""
        # By default, the category images button should be hidden
        self.assertFalse(self.config.iface_show_category_images,
                         "Category Images button should be hidden by default")

        # Enable the category images button
        self.config.write({'iface_show_category_images': True})
        self.assertTrue(self.config.iface_show_category_images,
                        "Category Images button should be shown after enabling")

        # Disable the category images button
        self.config.write({'iface_show_category_images': False})
        self.assertFalse(self.config.iface_show_category_images,
                         "Category Images button should be hidden after disabling")

    def test_pos_config_sales_report_button(self):
        """Test that the iface_show_sales_report field works correctly"""
        # By default, the sales report button should be hidden
        self.assertFalse(self.config.iface_show_sales_report,
                         "Sales Report button should be hidden by default")

        # Enable the sales report button
        self.config.write({'iface_show_sales_report': True})
        self.assertTrue(self.config.iface_show_sales_report,
                        "Sales Report button should be shown after enabling")

        # Disable the sales report button
        self.config.write({'iface_show_sales_report': False})
        self.assertFalse(self.config.iface_show_sales_report,
                         "Sales Report button should be hidden after disabling")

    def test_all_interface_controls_together(self):
        """Test that all interface control fields can be set together"""
        # Set all fields to True
        self.config.write({
            'iface_show_cash_control': True,
            'iface_show_product_images': True,
            'iface_show_category_images': True,
            'iface_show_sales_report': True,
        })

        self.assertTrue(self.config.iface_show_cash_control)
        self.assertTrue(self.config.iface_show_product_images)
        self.assertTrue(self.config.iface_show_category_images)
        self.assertTrue(self.config.iface_show_sales_report)

        # Set all fields to False
        self.config.write({
            'iface_show_cash_control': False,
            'iface_show_product_images': False,
            'iface_show_category_images': False,
            'iface_show_sales_report': False,
        })

        self.assertFalse(self.config.iface_show_cash_control)
        self.assertFalse(self.config.iface_show_product_images)
        self.assertFalse(self.config.iface_show_category_images)
        self.assertFalse(self.config.iface_show_sales_report)
